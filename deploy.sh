#!/bin/bash
set -euxo pipefail

# Directory for deployment
DEPLOY_DIR="/root/transportapp-web-app"

# Microsoft Teams webhook URL (replace with your actual URL or pass as an env variable)
TEAMS_WEBHOOK_URL="https://prod-09.centralindia.logic.azure.com:443/workflows/cb3efe45757b493f98cbea44f5820550/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=4EIzCCC9pu4QVxy6K_ZnrOjzM36Ckv9Q1oK1RwWOIi4"

# Function for cleanup on error
cleanup() {
    echo "An error occurred. Cleaning up..."
    cd $DEPLOY_DIR
    rm -f dist.tar.gz
    send_notification "Deployment Failed" "Deployment encountered an error during execution." "FF0000"
    exit 1
}

# Function to send notifications to Microsoft Teams
send_notification() {
    local title="$1"
    local message="$2"
    local color="$3"

    if [ -z "$TEAMS_WEBHOOK_URL" ]; then
        echo "TEAMS_WEBHOOK_URL is not set. Skipping notification."
        return
    fi

    local payload=$(cat <<EOF
{
  "type": "MessageCard",
  "context": "http://schema.org/extensions",
  "themeColor": "$color",
  "summary": "Bitbucket Deployment Notification",
  "sections": [{
    "activityTitle": "$title",
    "activitySubtitle": "Deployment at $(date)",
    "text": "$message",
    "markdown": true
  }]
}
EOF
    )

    curl -s -H "Content-Type: application/json" -d "$payload" "$TEAMS_WEBHOOK_URL"
}

# Set error trap
trap cleanup ERR

# Validate required environment variables
required_vars=("DEPLOY_DIR")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set"
        exit 1
    fi
done

# Determine docker compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    DOCKER_COMPOSE="docker compose"
fi

# Main deployment function
deploy() {
    echo ">>> Navigating to deployment directory..."
    cd $DEPLOY_DIR || { echo "Deployment directory does not exist! Exiting."; exit 1; }

    echo ">>> Removing old dist folder..."
    rm -rf dist || true

    echo ">>> Extracting new dist..."
    tar -xzf dist.tar.gz
    rm dist.tar.gz

    echo ">>> Stopping existing containers..."
    $DOCKER_COMPOSE down || true

    echo ">>> Starting containers with new build..."
    $DOCKER_COMPOSE up -d

    echo ">>> Verifying deployment..."
    if ! $DOCKER_COMPOSE ps | grep -q "Up"; then
        echo "Error: Containers failed to start properly"
        $DOCKER_COMPOSE logs
        send_notification "Deployment Failed" "Containers failed to start properly." "FF0000"
        exit 1
    fi

    echo ">>> Deployment complete."
    send_notification "Deployment Successful" "The application was successfully deployed." "00FF00"
}

# Execute the deployment
deploy
