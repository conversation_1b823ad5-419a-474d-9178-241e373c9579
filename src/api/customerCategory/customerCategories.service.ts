import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import {
  CustomerCategoriesCreateDto,
  ICustomerCategoriesResponse,
} from './customerCategories.types';

export class CustomerCategoriesService extends BaseService<
  ICustomerCategoriesResponse[],
  CustomerCategoriesCreateDto,
  CustomerCategoriesCreateDto
> {
  /**
   * Creates a new instance of ZoneService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/customer-portal/categories/');
  }
}
