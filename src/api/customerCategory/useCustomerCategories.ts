import { apiClient } from '..';
import { createEntityHooks } from '../core/react-query-hooks';
import { CustomerCategoriesService } from './customerCategories.service';
import {
  ICustomerCategoriesResponse,
  CustomerCategoriesCreateDto,
} from './customerCategories.types';

const customerCategoryService = new CustomerCategoriesService(apiClient.getAxiosInstance());
export const customerCategoryHook = createEntityHooks<
  ICustomerCategoriesResponse[],
  CustomerCategoriesCreateDto,
  CustomerCategoriesCreateDto
>('customerCategories', customerCategoryService);
