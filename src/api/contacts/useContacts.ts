import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { CreateCustomerContactDto, ICustomerContact } from './contacts.types';
import { ContactsService } from './contacts.service';

export const contactService = new ContactsService(apiClient.getAxiosInstance());

export const contactsHook = createEntityHooks<
  ICustomerContact,
  CreateCustomerContactDto,
  CreateCustomerContactDto
>('Contacts', contactService);
