import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { CreateVehicleTypeDto, GetVehicleTypeDto } from './vehicleType.types';

/**
 * Service class for managing zones
 */
export class VehicleTypes extends BaseService<
  GetVehicleTypeDto,
  CreateVehicleTypeDto,
  CreateVehicleTypeDto
> {
  /**
   * Creates a new instance of VehicleType
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/vehicleTypes');
  }
}
