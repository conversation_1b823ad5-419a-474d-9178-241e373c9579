export interface ICategories {
  id?: string;
  name: string;
}

export interface IPermissionsForContacts {
  prices: boolean;
  address: boolean;
  invoices: boolean;
}

export interface IPackages {
  id?: string;
  orderId?: string;
  packageTemplateId: string;
  packageTemplateName?: string;
  itemType: string;
  quantity: number;
  totalWeight: number;
  weightUnit: string;
  length: number;
  width: number;
  height: number;
  dimensionUnit: string;
  volume?: number;
  declaredValue: number;
  description: string;
  notes: string;
  imageUrl: any;
  createdAt?: string;
  updatedAt?: string;
}

export interface IModifier {
  amount: number;
  configuration: string;
  id: string;
  name: string;
}

export interface IOrder {
  customerName?: string;
  referenceNumber?: string;
  collectionAddressId: string;
  collectionContactName?: string;
  collectionInstructions?: string;
  collectionSignatureRequired?: boolean;
  scheduledCollectionTime?: string;
  scheduledDeliveryTime?: string;
  deliveryAddressId: string;
  deliveryContactName?: string;
  deliveryInstructions?: string;
  deliverySignatureRequired?: boolean;
  collectionCompanyName?: string;
  deliveryCompanyName?: string;
  deliveryAddress?: string;
  collectionAddress?: string;
  serviceLevel?: string;
  declaredValue?: number;
  codAmount?: number;
  priceSetId?: string;
  description?: string;
  comments?: string;
  internalNotes?: string;
  items?: IPackages[];
  customFields?: {
    [key: string]: string;
  };
  metadata?: {
    [key: string]: string;
  };
  status?: string;
  id?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  trackingDetails?: {
    [key: string]: any;
  };
  totalPrice?: number;
  trackingNumber?: string;
  requestedByName?: string;
  actualDeliveryTime?: string;
  totalWeight?: number;
  estimatedDuration?: number;
  assignedDriverName?: string;
  collectionEmail?: string;
  deliveryEmail?: string;
  collectionPhone?: string;
  deliveryPhone?: string;
  collectionPhoneExtension?: string;
  deliveryPhoneNumber?: string;
  deliveryPhoneExtension?: string;
  dueDate?: string;
  attachments?: OrderAttachments;
}
export interface IAttachments {
  id: string;
  originalName: string;
  url: string;
  type: string;
  mimeType: string;
}

export interface OrderAttachments {
  pickupSignature: IAttachments | null;
  deliverySignature: IAttachments | null;
  images: IAttachments[];
  files: IAttachments[];
}
export interface IOrderDetailsForPlaceOrder {
  collectionAddressId: string;
  deliveryAddressId: string;
  scheduledCollectionTime?: string;
  totalItems: number;
  declaredValue?: number;
  items: IPackages[];
}
export interface OrderPaginatedResponse {
  data: IOrder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
