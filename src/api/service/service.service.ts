import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { CreateServiceDto, GetAllServiceDto } from './service.types';

export class ScheduleService extends BaseService<
  GetAllServiceDto,
  CreateServiceDto,
  CreateServiceDto
> {
  /**
   * Creates a new instance of Address
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/customer-portal/pricing/available-services');
  }
}
