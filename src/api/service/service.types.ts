import { IModifier } from '../order/order.types';

export interface serviceOrderRequest {
  description: string;
}

export interface ServicePricingDto {
  basePrice: number;
  totalPrice: number;
  requiredTotalPrice: number;
  totalModifierPrice: number;
  selectedModifierPrice: number;
  modifiers: IModifier[];
}

export interface CreateServiceDto {
  pickupDate: string;
  includePricing: boolean;
  order: any;
}

export interface IGetAvailableServices {
  id: string;
  name: string;
  deliveryDate: string;
  pricing: ServicePricingDto;
}
export interface GetAllServiceDto {
  data: IGetAvailableServices[];
}
