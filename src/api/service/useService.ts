import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { apiClient } from '..';
import { ScheduleService } from './service.service';
import { CreateServiceDto, GetAllServiceDto } from './service.types';

export const scheduleService = new ScheduleService(apiClient.getAxiosInstance());

export const scheduleServiceHook = createEntityHooks<
  GetAllServiceDto,
  CreateServiceDto,
  CreateServiceDto
>('scheduleService', scheduleService);
