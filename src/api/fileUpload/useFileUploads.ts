import { QueryHook<PERSON>ey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { GetUploadedFileDto, IUploadFileDto } from './fileUpload.types';
import { FileUploadService } from './fileUploads.service';

export const fileUploadService = new FileUploadService(apiClient.getAxiosInstance());

export const fileUploadServiceHook = createEntityHooks<
  GetUploadedFileDto,
  IUploadFileDto,
  IUploadFileDto
>(QueryHookKey.filUploads, fileUploadService);
