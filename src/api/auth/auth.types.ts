import { UPlans } from '@/types/enums/Plans';
import { Roles } from '@/types/enums/Roles';

export interface ILogin {
  email: string;
  password: string;
}

export interface IUser {
  id: string;
  email: string;
  name: string;
  tenantId: string;
  role: Roles;
  plan: UPlans;
  status: string;
  userId: string;
  emailVerified: boolean;
  phoneNumber: string;
  isPrimary: boolean;
  isDeleted: boolean;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string;
  lockedUntil: string | null;
  loginCount: number;
  failedAttempts: number;
  metadata: {
    title: string;
    notes: string;
  };
  permissions: {
    prices: boolean;
    address: boolean;
    invoices: boolean;
  };
}
