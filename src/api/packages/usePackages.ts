import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { PackagesService } from './packages.service';
import { IPackage, IPackagesList } from './packages.types';

export const packageService = new PackagesService(apiClient.getAxiosInstance());

export const packageServiceHook = createEntityHooks<IPackagesList, IPackage, IPackage>(
  'Packages',
  packageService
);
