export interface IPackage {
  id: string;
  name: string;
  description?: string;
  status: string;
  capabilities: string[];
  dimensionsRequired: boolean;
  declaredValue?: number;
  weightRequired: boolean;
  maxWeight?: number;
  maxVolume?: number;
  requiresSignature: number;
  requiresInsurance: number;
  specialHandlingInstructions?: string;
  metadata: {
    isPrimary: boolean;
  };
  height: number;
  width: number;
  length: number;
  quantity: number;
  cubicDimention: string;
  totalWeight: number;
  combinedWeight?: number;
}

export interface IPackagesList {
  packages: IPackage[];
}
