/**
 *  Defines which roles can access each route.
 */

import { Roles } from '@/types/enums/Roles';

export const ROUTE_ROLE_RESTRICTION = {
  CUSTOMER_CUSTOMERS: [Roles.TENANT, Roles.DISPATCHER],
  CUSTOMER_PARTNER: [Roles.TENANT],
  CUSTOMER_BILLING: [Roles.TENANT, Roles.DISPATCHER],
  CUSTOMER_GENERALTAB: [Roles.TENANT],
  LOGISTICS_ORDERS: [Roles.TENANT, Roles.DISPATCHER],
  LOGISTICS_DISPATCHER: [Roles.TENANT, Roles.DISPATCHER],
  LOGISTICS_VEHICLE: [Roles.TENANT, Roles.DISPATCHER],

  LOCATION_ADDRESS: [Roles.TENANT, Roles.DISPATCHER],
  LOCATION_ZONE: [Roles.TENANT, Roles.DISPATCHER],
  LOCATION_ROUTES: [Roles.TENANT, Roles.DISPATCHER],

  SETTINGS_GENERAL: [Roles.TENANT],
  SETTINGS_PARTNERS: [Roles.TENANT],
  SETTINGS_TEMPLATES: [Roles.TENANT],

  PRICES_PRICE_SETS: [Roles.TENANT],
  PRICES_PRICE_MODIFIERS: [Roles.TENANT],
};
