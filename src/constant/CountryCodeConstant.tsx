import { translator } from '@/i18n/languageLoader';

export const optionsForPrefix = [
  {
    value: 'USA',
    label: translator('common.usa'),
    mask: '(000)000-0000',
    length: 10,
    geoCountryCode: 'Usa',
    dialCode: '+1',
    countryCode: 'USA +1',
  },
  {
    value: 'IND',
    label: translator('common.ind'),
    mask: '00000-00000',
    length: 10,
    geoCountryCode: 'India',
    dialCode: '+91',
    countryCode: 'IND +91',
  },
  {
    value: 'CAN',
    label: translator('common.can'),
    mask: '(000)000-0000',
    length: 10,
    geoCountryCode: 'Canada',
    dialCode: '+1',
    countryCode: 'CAN +1',
  },
];
