import React from 'react';
import { COLORS, PRIMARY, ERROR, SEMANTIC } from '../styles/colorConstants';

/**
 * Example component demonstrating how to use the color constants
 */
const ColorUsageExample: React.FC = () => {
  // Inline style example
  const containerStyle: React.CSSProperties = {
    backgroundColor: SEMANTIC.BACKGROUND_LIGHT,
    border: `1px solid ${SEMANTIC.BORDER_DEFAULT}`,
    padding: '20px',
    borderRadius: '8px',
  };

  const headingStyle: React.CSSProperties = {
    color: SEMANTIC.TEXT_PRIMARY,
    marginBottom: '16px',
  };

  const buttonPrimaryStyle: React.CSSProperties = {
    backgroundColor: PRIMARY[600],
    color: COLORS.COMMON.WHITE,
    border: 'none',
    padding: '8px 16px',
    borderRadius: '4px',
    marginRight: '8px',
    cursor: 'pointer',
  };

  const buttonDangerStyle: React.CSSProperties = {
    backgroundColor: ERROR[600],
    color: COLORS.COMMON.WHITE,
    border: 'none',
    padding: '8px 16px',
    borderRadius: '4px',
    cursor: 'pointer',
  };

  return (
    <div style={containerStyle}>
      <h2 style={headingStyle}>Color Constants Example</h2>
      <p style={{ color: SEMANTIC.TEXT_SECONDARY, marginBottom: '16px' }}>
        This component demonstrates how to use the color constants in your components.
      </p>
      <div>
        <button style={buttonPrimaryStyle}>Primary Button</button>
        <button style={buttonDangerStyle}>Danger Button</button>
      </div>
    </div>
  );
};

export default ColorUsageExample;

/**
 * Example with styled-components (commented out)
 * Uncomment if you're using styled-components in your project
 */
/*
import styled from 'styled-components';

const Container = styled.div`
  background-color: ${SEMANTIC.BACKGROUND_LIGHT};
  border: 1px solid ${SEMANTIC.BORDER_DEFAULT};
  padding: 20px;
  border-radius: 8px;
`;

const Heading = styled.h2`
  color: ${SEMANTIC.TEXT_PRIMARY};
  margin-bottom: 16px;
`;

const Text = styled.p`
  color: ${SEMANTIC.TEXT_SECONDARY};
  margin-bottom: 16px;
`;

const ButtonPrimary = styled.button`
  background-color: ${PRIMARY[600]};
  color: ${COLORS.COMMON.WHITE};
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
  
  &:hover {
    background-color: ${PRIMARY[500]};
  }
`;

const ButtonDanger = styled.button`
  background-color: ${ERROR[600]};
  color: ${COLORS.COMMON.WHITE};
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: ${ERROR[500]};
  }
`;
*/
