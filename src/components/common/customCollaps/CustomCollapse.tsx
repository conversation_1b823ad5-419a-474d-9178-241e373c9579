import { CollapseDownIcon } from '@/assets/icons/collapseDownIcon';
import { CollapseUpIcon } from '@/assets/icons/collapseUpIcon';
import { Collapse, CollapsePanelProps, CollapseProps } from 'antd';
import './customCollapse.css';

export const CustomCollapse = ({
  defaultActiveKey,
  collapseProps,
  collapsePanelProps,
  className,
  items,
}: {
  defaultActiveKey: string[];
  collapseProps?: CollapseProps;
  collapsePanelProps?: CollapsePanelProps;
  className?: string;
  items: { key: string; label: string; children: React.ReactNode }[];
}) => (
  <Collapse
    expandIconPosition="end"
    expandIcon={({ isActive }) => (isActive ? <CollapseDownIcon /> : <CollapseUpIcon />)}
    defaultActiveKey={defaultActiveKey}
    className={`order-details-collapse ${className || ''}`}
    bordered={false}
    collapsible="icon"
    {...collapseProps}
  >
    {items.map(({ key, label, children }) => (
      <Collapse.Panel header={label} key={key} {...collapsePanelProps}>
        {children}
      </Collapse.Panel>
    ))}
  </Collapse>
);
