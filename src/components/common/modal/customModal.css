.custom-modal-mask {
  background-color: #0876a40a !important;
}
.custom-modal {
  width: 100% !important;
  max-width: 684px;
}
.custom-modal-body {
  padding: 16px 24px !important;
  max-height: 516px;
  overflow-y: scroll;
}

.custom-modal-content {
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden;
  font-family: var(--font-family);
}

.custom-modals-header {
  box-shadow: 0px -1px 0px 0px #cdd7db inset;
  @apply bg-[#F5F6FF] py-4 px-6;
}
.custom-modal-header {
  margin-bottom: 2px !important;
}

.custom-modal-close {
  top: 22px !important;
}

.custom-modal-footer {
  box-shadow: 0px 1px 0px 0px #cdd7db inset !important;
  background-color: #f5f6ff !important;
  padding: 10px 16px !important;
}
