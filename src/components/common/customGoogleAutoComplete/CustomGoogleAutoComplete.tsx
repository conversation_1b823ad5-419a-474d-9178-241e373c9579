import ConfigManager from '@/lib/ConfigManager';
import { Autocomplete, AutocompleteProps } from '@react-google-maps/api';
import { memo } from 'react';

interface CustomGoogleAutoCompleteProps extends AutocompleteProps {
  ref: React.RefObject<Autocomplete>;
}

const CustomGoogleAutoComplete = (props: CustomGoogleAutoCompleteProps) => {
  const center = {
    lat: ConfigManager.get('mapCenter')?.lat as number,
    lng: ConfigManager.get('mapCenter')?.lng as number,
  };

  const radiusMultiplier = 1; // 1 = 100km

  const Bounds = {
    north: center?.lat + radiusMultiplier,
    south: center?.lat - radiusMultiplier,
    east: center?.lng + radiusMultiplier,
    west: center?.lng - radiusMultiplier,
  };

  const options = {
    ...props.options,
    bounds: center && Bounds,
    strictBounds: true,
  };

  return (
    <Autocomplete {...props} options={options}>
      {props.children}
    </Autocomplete>
  );
};

export default memo(CustomGoogleAutoComplete);
