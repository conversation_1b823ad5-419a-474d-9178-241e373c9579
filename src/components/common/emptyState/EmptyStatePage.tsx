import { Empty, Typography } from 'antd';
import { emptyStateIcon } from '@/assets';
import './EmptyStatePage.css';
import { IEmptyStatePageProps } from './emptyStateTypes';

export const EmptyStatePage: React.FC<IEmptyStatePageProps> = (props) => {
  const { title, description, link, onLinkAction, image } = props;
  return (
    <div className="flex items-center justify-center flex-1">
      <div>
        <Empty
          className="empty-state-image"
          image={image || emptyStateIcon}
          description={
            <div className="flex flex-col">
              <span className="text-[25px] font-[500] !text-[#96A9B1]">{title}</span>
              <div className="flex gap-1 justify-center">
                <span className="text-[15px] font-[500] !text-[#96A9B1]">{description}</span>
                <Typography.Link
                  className="text-[15px] font-[500] !text-primary-600 !underline !decoration-primary-600"
                  onClick={() => onLinkAction?.()}
                >
                  {link}
                </Typography.Link>
              </div>
            </div>
          }
        />
      </div>
    </div>
  );
};
