/* eslint-disable no-debugger */
import React, { ReactNode } from 'react';
import './mainLayout.css';
import Footer from '../footer';
import Header from '../header/header';

interface HeaderProps {
  children?: ReactNode;
}

const MainLayout: React.FC<HeaderProps> = ({ children }) => {
  return (
    <div className="flex flex-col h-full min-h-screen">
      <Header />
      <div className="flex-grow">{children}</div>
      <Footer />
    </div>
  );
};

export default MainLayout;
