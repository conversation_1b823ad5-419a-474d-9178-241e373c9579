.app-header {
  width: 100%;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
}

.header-container {
  max-width: 100%;
  padding: 0 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .header-container {
    padding: 0 1.5rem;
  }
}

.header-logo {
  flex-shrink: 0;
  margin-right: 0.5rem;
}

@media (min-width: 768px) {
  .header-logo {
    margin-right: 2rem;
  }
}

.logo-wrapper {
  display: flex;
  align-items: center;
  padding: 1rem 0;
}
.logo-wrapper-without-content {
  width: 100%;
  justify-content: center;
}

.mobile-menu-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: #4b5563;
  background-color: transparent;
}

.mobile-menu-button:hover {
  color: #1f2937;
  background-color: #f3f4f6;
}

.profile-dropdown {
  position: absolute;
  right: 0;
  z-index: 10;
  margin-top: 0.5rem;
  width: 12rem;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  top: 100%;
}

.mobile-profile-dropdown {
  position: absolute;
  right: 1rem;
  z-index: 10;
  margin-top: 0.5rem;
  width: 12rem;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  top: 3.5rem;
}

.mobile-menu {
  padding: 0.5rem;
  margin-top: 0.25rem;
  margin-bottom: 0.75rem;
  background-color: white;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}

.active-mobile-link {
  color: #4338ca;
  background-color: #eef2ff;
}

.inactive-mobile-link {
  color: #4b5563;
}
