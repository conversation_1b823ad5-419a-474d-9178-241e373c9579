import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Space } from 'antd';
import type { MenuProps } from 'antd';

export const FooterLanguageToggle: React.FC = () => {
  const { currentLanguage, setLanguage } = useLanguage();

  const items: MenuProps['items'] = [
    {
      key: 'EN',
      label: 'English',
      onClick: () => setLanguage('EN'),
    },
    {
      key: 'FR',
      label: 'Français',
      onClick: () => setLanguage('FR'),
    },
  ];

  return (
    <Dropdown menu={{ items }} trigger={['click']}>
      <a onClick={(e) => e.preventDefault()}>
        <Space className="text-gray-600 text-xs">
          <span className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-1"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="2" y1="12" x2="22" y2="12"></line>
              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
            </svg>
            {currentLanguage === 'EN' ? 'English' : 'Français'}
          </span>
          <DownOutlined style={{ fontSize: '10px' }} />
        </Space>
      </a>
    </Dropdown>
  );
};
