import { useRef, useState } from 'react';
import { Button, Checkbox, Popover } from 'antd';
import { IColumnManageProps } from '../types';
import { useColumnManagement } from '../hooks/useColumnManagement';
import { useAutoScroll } from '../hooks/useAutoScroll';
import { SearchInput } from './SearchInput';
import { ColumnList } from './ColumnList';
import { useLanguage } from '@hooks/useLanguage.ts';
import PermissionChecker from '@components/specific/permissionChecker/PermissionChecker.tsx';
import { ColumnMergeIcon } from '@assets/icons/columnMergeIcon.tsx';
import { infoCircleOutlined } from '@/assets';
import { useClickOutside } from '@components/specific/columnManage/hooks/useClickOutside.ts';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import '../ColumnManage.css';

export const ColumnManage = ({ colDefs, gridName, className }: IColumnManageProps) => {
  const [isColumnManageOpen, setIsColumnManageOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [draggingColumnId, setDraggingColumnId] = useState('');

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { t } = useLanguage();

  const {
    columns,
    setColumns,
    searchedColumns,
    setSearchedColumns,
    filteredColumns,
    handleSelectAll,
    handleColumnSelect,
    resetColumns,
    applyColumns,
  } = useColumnManagement(colDefs, gridName);

  useAutoScroll(scrollContainerRef, isDragging);

  useClickOutside(containerRef, () => {
    setIsColumnManageOpen(false);
    setSearchedColumns('');
  });

  const handleDraggingItem = (id: string) => {
    setIsDragging(true);
    setDraggingColumnId(id);
  };

  const handleDragStop = () => {
    setIsDragging(false);
    setDraggingColumnId('');
  };

  return (
    <div className={`main-container ${className || ''}`}>
      <PermissionChecker feature="manageColumn" type="popover">
        <Popover
          trigger="click"
          open={isColumnManageOpen}
          onOpenChange={(visible) => setIsColumnManageOpen(visible)}
          content={
            <div ref={containerRef} className="manage-columns-container">
              <div className="manage-columns-header">
                <h2 className="column-manage-title">Column manage</h2>
                <CustomTooltip title={''}>
                  <img src={infoCircleOutlined} alt="info" />
                </CustomTooltip>
              </div>

              <div className="inside-content-container">
                <SearchInput value={searchedColumns} onChange={setSearchedColumns} />

                <p className="uncheck-info-text">{t('columnMange.uncheckColumn')}</p>

                <div className="scroll-container" ref={scrollContainerRef}>
                  {!searchedColumns && (
                    <div className="select-all-container">
                      <Checkbox
                        id="select-all-tailwind"
                        checked={columns.filter((col) => !col.visible).length <= 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="select-all-checkbox"
                      />
                      <label htmlFor="select-all-tailwind" className="select-all-label">
                        {t('columnMange.selectAll')}
                      </label>
                    </div>
                  )}

                  <ColumnList
                    filteredColumns={searchedColumns ? filteredColumns : columns}
                    isDraggable={Boolean(!searchedColumns)}
                    onColumnSelect={handleColumnSelect}
                    onReorder={setColumns}
                    draggingColumnId={draggingColumnId}
                    onDragStart={handleDraggingItem}
                    onDragEnd={handleDragStop}
                  />
                </div>
              </div>

              <div className="button-container">
                <Button onClick={resetColumns} className="reset-button">
                  {t('columnMange.resetToDefault')}
                </Button>
                <Button
                  onClick={async () => {
                    const isSuccessfullyColumnApplied = await applyColumns();
                    isSuccessfullyColumnApplied && setIsColumnManageOpen(false);
                  }}
                  className="apply-button"
                >
                  {t('columnMange.apply')}
                </Button>
              </div>
            </div>
          }
          placement="bottomRight"
        >
          <Button
            className={
              isColumnManageOpen ? 'column-manager-button-active' : 'column-manager-button'
            }
            icon={<ColumnMergeIcon bool={isColumnManageOpen} />}
            onClick={() => setIsColumnManageOpen(true)}
          />
        </Popover>
      </PermissionChecker>
    </div>
  );
};
