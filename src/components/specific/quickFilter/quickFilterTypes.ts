import { FilterParams, PaginationParams } from '@/api/core/types';
import { QuickFilterGridKeys } from '@/constant/SettingsKeys';
import { AppEvents } from '@/types/AppEvents';
import { SelectProps } from 'antd';
import { IAssignedFilters } from '../activeFilters/activeFiltersTypes';

export interface IQuickFilterProps {
  /**
   * Unique event identifier used to trigger quick filter events
   */
  eventKey: `${QuickFilterGridKeys}`;
  /**
   * Unique event identifier used to trigger quick filter title events
   */
  quickFilterTitleEventKey: keyof AppEvents;
  clearAllToDefault: () => void;
  setFilterParams: (value: React.SetStateAction<PaginationParams & FilterParams>) => void;
}

export type LabelRender = SelectProps['labelRender'];

export interface IQuickFilterPayload {
  name: string;
  filters: IAssignedFilters[];
}
