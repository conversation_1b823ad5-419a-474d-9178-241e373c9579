import { Select, Typography } from 'antd';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import { deleteSvg } from '@/assets';
import { updateSetting } from '@/api/settings/settings.service';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { on } from '@/contexts/PulseContext';
import { AppEvents, IQuickFilterEvent } from '@/types/AppEvents';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { IQuickFilterPayload, IQuickFilterProps, LabelRender } from './quickFilterTypes';
import { ISelectOption } from '@/types/CommonTypes';
import { FilterParams, PaginationParams } from '@/api/core/types';
import { IAssignedFilters } from '../activeFilters/activeFiltersTypes';

const QuickFilter = (props: IQuickFilterProps) => {
  const { eventKey, quickFilterTitleEventKey, clearAllToDefault, setFilterParams } = props;
  const [eventData, setEventData] = useState({} as IQuickFilterEvent);
  const [selectedFilterValue, setSelectedFilterValue] = useState<ISelectOption | string>();

  useEffect(() => {
    if (eventKey) {
      on(eventKey as keyof AppEvents, (data) => {
        setEventData(data as IQuickFilterEvent);
      });
    }
  }, [eventKey]);

  useEffect(() => {
    if (quickFilterTitleEventKey) {
      on(quickFilterTitleEventKey, (data) => {
        if ('title' in data) {
          setSelectedFilterValue(data.title ? { label: data.title, value: data.title } : '1');
        }
      });
    }
  }, [quickFilterTitleEventKey]);

  const { t } = useLanguage();
  const notificationManager = useNotificationManager();

  const handleDeleteQuickFilter = useCallback(
    (selectedFilter: string) => {
      customAlert.error({
        message: t('ordersPage.areYouSureDeleteQuickFilter'),
        title: t('ordersPage.deleteQuickFilter'),
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
        firstButtonFunction: async () => {
          const filteredQuickFilters = eventData?.quickFilter?.value.filter(
            (filteredItem) => filteredItem?.name !== selectedFilter
          );

          if (eventData?.quickFilter?.id) {
            await updateSetting(eventData.quickFilter.id, {
              ...eventData.quickFilter,
              value: filteredQuickFilters,
            });
            await eventData.refetchFilters();
            clearAllToDefault();
            setSelectedFilterValue('1');
            setFilterParams((prev: PaginationParams & FilterParams) => ({
              pageNumber: prev.pageNumber,
              pageSize: prev.pageSize,
              searchTerm: prev.searchTerm,
              sortDirection: prev.sortDirection,
            }));
            notificationManager.success({
              message: t('common.success'),
              description: t('quickFilter.quickFilterDeletedSuccessfully'),
            });
            customAlert.destroy();
          }
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
    },
    [eventData, notificationManager, setFilterParams, clearAllToDefault, t]
  );

  const quickFilterOptions = useMemo(() => {
    return (eventData.quickFilter?.value as { name: string }[])?.map((filter) => ({
      label: filter.name,
      value: filter.name,
    }));
  }, [eventData?.quickFilter?.value]);

  const labelRender: LabelRender = (props) => {
    return props.label || <span>{t('quickFilter.quickFilterLabel')}</span>;
  };

  const optionRenderer = (option: { data: ISelectOption }) => (
    <div className="flex w-full items-center justify-between">
      <Typography.Text ellipsis>{option.data.label}</Typography.Text>
      <img
        src={deleteSvg}
        alt="delete"
        width={18}
        onClick={(e) => {
          e.stopPropagation();
          handleDeleteQuickFilter(option.data.label);
        }}
      />
    </div>
  );

  const onSelectChangeHandler = async (
    selectedFilterName: string | ISelectOption,
    currentOption: ISelectOption
  ) => {
    setSelectedFilterValue(currentOption);
    const selectedFilter = eventData?.quickFilter.value.find(
      (filter) => filter.name === selectedFilterName
    ) as IQuickFilterPayload;

    if (!selectedFilter) return;

    const mappedFilters: IAssignedFilters[] = selectedFilter.filters.map((filter) => {
      if (filter.type === 'date') {
        const normalizedValue = Array.isArray(filter.value)
          ? [dayjs.utc(filter.value[0]), dayjs.utc(filter.value[1])]
          : dayjs.utc(filter.value);

        return {
          ...filter,
          value: normalizedValue,
        };
      }

      return filter;
    });

    eventData?.onFilterApply({ filters: mappedFilters });
  };

  return (
    <Select
      labelRender={labelRender}
      defaultValue={'1'}
      value={selectedFilterValue}
      style={{ width: '100%' }}
      options={quickFilterOptions}
      prefixCls="custom-select"
      className="min-w-[150px] max-w-[400px] md:w-full"
      suffixIcon={<SelectDownArrow />}
      optionRender={optionRenderer}
      onSelect={onSelectChangeHandler}
    />
  );
};
export default memo(QuickFilter);
