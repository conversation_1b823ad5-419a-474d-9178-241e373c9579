import React, { ChangeEvent, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Form,
  FormListFieldData,
  Input,
  Popover,
  Radio,
  Select,
  Space,
} from 'antd';

import {
  FilterBoxDeleteOutlinedIcon,
  FilterBoxPlusOutlinedIcon,
  FilterInputIcon,
  infoCircleOutlined,
} from '@/assets';
import { StringOperators, NumberOperators, DateOperators } from '@/constant/SearchFilterOperators';
import { useLanguage } from '@/hooks/useLanguage';
import { handleKeyPress } from '@/lib/FormValidators';
import './SearchFilter.css';
import { formErrorRegex } from '@/constant/Regex';

import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';

import { addSetting, updateSetting, useGetSettings } from '@/api/settings/settings.service';
import { useGetCurrentUser } from '@/api/auth/auth.service';
import { TrackedError } from '@/types/AxiosTypes';
import { AxiosError } from 'axios';
import { emit } from '@/contexts/PulseContext';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { FilterParams, PaginationParams } from '@/api/core/types';
import { IQuickFilterPayload } from '../quickFilter/quickFilterTypes';
import { SettingsScope } from '@/constant/SettingsScope';
import { AppEvents } from '@/types/AppEvents';
import { GridStorageKey, IColDef } from '@/types/AgGridTypes';

interface OptionsInterface {
  value: string;
  label: string;
  type: string;
}
const { RangePicker } = DatePicker;
const { Search } = Input;
export interface SearchedProps {
  searchedValues?: (e: string) => void;
  colDefs: IColDef[];
  searchInputPlaceholder?: string;
  className?: string;
  advanceFilter?: boolean;
  clearAllFunctionRef?: React.MutableRefObject<{
    handleClearAll: () => void;
  }>;
  onSearch?: (value: string) => void;
  supportedFields?: string[];
  searchText?: string;
  setSearchText?: React.Dispatch<React.SetStateAction<string>>;
}
interface IAssignedFilters {
  field: string;
  operator: string;
  value: string;
  label: string;
}
export interface IWithQuickFilter {
  isSetQuickFilter?: true;
  setQuickFilters: (filters: string, data: IAssignedFilters[]) => void;

  /**
   * Unique event identifier used to trigger quick filter events
   */
  quickFilterEventKey: keyof AppEvents;
  /**
   * Unique event identifier used to trigger quick filter title events
   */
  quickFilterTitleEventKey: keyof AppEvents;
  /**
   * Key used to store quick filter settings in the backend
   */
  quickFilterSettingsKey: GridStorageKey;
}

export interface IWithoutQuickFilter {
  isSetQuickFilter?: false;
}

export interface IWithoutAdvanceFilter {
  advanceFilter?: false;
}

export interface IWithAdvanceFilter {
  setSelectedQuickFilterData: React.Dispatch<React.SetStateAction<IAssignedFilters[]>>;
  setFilterParams: (value: React.SetStateAction<PaginationParams & FilterParams>) => void;
  onFilterApply: (data: { filters: IAssignedFilters[] }) => void;
}

export interface IAdvanceFilterObj {
  where: Record<string, Record<any, string>>;
}
export type ISearchedProps = SearchedProps &
  (IWithQuickFilter | IWithoutQuickFilter) &
  (IWithAdvanceFilter | IWithoutAdvanceFilter);

const SearchFilterComponent: React.FC<ISearchedProps> = (props) => {
  const {
    colDefs,
    isSetQuickFilter,
    searchInputPlaceholder,
    advanceFilter = true,
    supportedFields = [],
    className = '',
    onSearch,
    clearAllFunctionRef,
    searchText,
    setSearchText,
  } = props;
  const { setQuickFilters, quickFilterEventKey, quickFilterSettingsKey, quickFilterTitleEventKey } =
    props as IWithQuickFilter;
  const { setFilterParams, setSelectedQuickFilterData, onFilterApply } =
    props as IWithAdvanceFilter;
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  //TODO: Remove this state
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_isDropDownOpen, setIsDropDownOpen] = useState<boolean>(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean>(false);
  const [selectedFields, setSelectedFields] = useState<(OptionsInterface | null)[]>([]);
  const [lastAppliedFilters, setLastAppliedFilters] = useState<IAssignedFilters[]>([]);
  const [isFilterName, setIsFilterName] = useState<boolean>(false);
  const [isAppliedOnlyFilter, setIsAppliedOnlyFilter] = useState<boolean>(false);
  const filterRef = useRef<HTMLDivElement>(null);
  const { t } = useLanguage();
  const [form] = Form.useForm();

  const mappedValues: OptionsInterface[] = useMemo(() => {
    return colDefs
      .filter(
        (col) => col.headerName !== 'Action' && supportedFields?.includes(col.field as string)
      )
      ?.map((value) => {
        return {
          value: value.field as string,
          label: value.headerName as string,
          type: value.type as string,
        };
      });
  }, [colDefs, supportedFields]);

  const { data: user } = useGetCurrentUser();

  const addQuickFilterIfNotExist = (error: AxiosError) => {
    const errorStack = error?.response?.data as TrackedError;
    if (errorStack?.code === '414001' && isSetQuickFilter && quickFilterSettingsKey) {
      addSetting({
        userId: user?.id as string,
        scope: SettingsScope.USER,
        key: quickFilterSettingsKey as string,
        value: [],
      }).then(async () => {
        await refetchFilters();
      });
    }
    return false;
  };

  const { data: quickFilter, refetch: refetchFilters } = useGetSettings(
    quickFilterSettingsKey,
    user?.id,
    {
      retry: 0,
      throwOnError: addQuickFilterIfNotExist,
      enabled: Boolean(!!user?.id && quickFilterSettingsKey),
    }
  );

  const isBetweenOperator = (index: number) => {
    return Object.keys(form.getFieldsValue()).length > 0
      ? form.getFieldsValue()?.filters[index]?.operator === 'between'
      : false;
  };

  const handleOnChange = useCallback(
    (_value: string, option: OptionsInterface | OptionsInterface[], index: number) => {
      const newSelectedFields = [...selectedFields];
      if (Array.isArray(option)) {
        newSelectedFields[index] = null;
      } else {
        newSelectedFields[index] = option;
      }
      setSelectedFields(newSelectedFields);
    },
    [selectedFields]
  );

  /**
   * Renders appropriate form fields based on the selected field type.
   *
   * @param {FormListFieldData} field - The form list field data.
   * @param {number} index - The index of the current field in the list.
   * @returns {JSX.Element} The JSX elements for the form fields.
   *
   * As we are using dynamic form fields, we need to render dynamic fields in a list.
   * This function returns JSX elements for each form field based on the selected field type.
   */
  const renderFields = (field: FormListFieldData, index: number) => {
    const selectedField = selectedFields[index] || mappedValues[0];
    switch (selectedField?.type || 'string') {
      case 'string':
        return (
          <>
            <Form.Item
              name={[field.name, 'operator']}
              rules={[{ required: true, message: `${t('searchFilterBox.selectOption')}` }]}
            >
              <Select
                className="w-full min-w-[200px]"
                options={StringOperators}
                placeholder={`${t('searchFilterBox.selectCondition')}`}
                onClick={(e) => e.stopPropagation()}
              />
            </Form.Item>
            <Form.Item
              name={[field.name, 'value']}
              rules={[
                { required: true, message: `${t('searchFilterBox.enterInput')}` },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: `${t('searchFilterBox.noMultiSpaces')}`,
                },
              ]}
            >
              <Input
                maxLength={100}
                placeholder={`Enter ${selectedField?.label || mappedValues[0]?.label}`}
              />
            </Form.Item>
          </>
        );
      case 'email':
        return (
          <>
            <Form.Item
              name={[field.name, 'value']}
              rules={[
                { required: true, message: `${t('searchFilterBox.enterInput')}` },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: `${t('searchFilterBox.noMultiSpaces')}`,
                },
              ]}
            >
              <Input maxLength={100} placeholder="Search by email" type="email" />
            </Form.Item>
          </>
        );
      case 'number':
        return (
          <>
            <Form.Item
              name={[field.name, 'operator']}
              rules={[{ required: true, message: `${t('searchFilterBox.selectOption')}` }]}
            >
              <Select
                className="w-full min-w-[200px]"
                options={NumberOperators}
                placeholder={`${t('searchFilterBox.selectCondition')}`}
                onClick={(e) => e.stopPropagation()}
              />
            </Form.Item>
            <Form.Item
              className="!w-full"
              name={[field.name, 'value']}
              rules={[{ required: true, message: `${t('searchFilterBox.enterNumber')}` }]}
            >
              <Input
                onKeyDown={(e) => handleKeyPress(e)}
                maxLength={100}
                placeholder={`${t('searchFilterBox.filterNumber')}`}
                type="number"
              />
            </Form.Item>
          </>
        );
      case 'date':
        return (
          <>
            <Form.Item
              name={[field.name, 'operator']}
              rules={[{ required: true, message: `${t('searchFilterBox.selectDate')}` }]}
            >
              <Select
                className="w-full min-w-[200px]"
                options={DateOperators}
                placeholder={`${t('searchFilterBox.selectCondition')}`}
                onDropdownVisibleChange={(open: boolean) => setIsDropDownOpen(open)}
                onClick={(e) => e.stopPropagation()}
              />
            </Form.Item>
            {isBetweenOperator(index) ? (
              <Form.Item
                name={[field.name, 'value']}
                rules={[{ required: true, message: `${t('searchFilterBox.selectDate')}` }]}
              >
                <RangePicker className="!w-full" format="DD-MM-YYYY" />
              </Form.Item>
            ) : (
              <Form.Item
                name={[field.name, 'value']}
                rules={[{ required: true, message: `${t('searchFilterBox.selectDate')}` }]}
              >
                <DatePicker format="DD-MM-YYYY" />
              </Form.Item>
            )}
          </>
        );
      case 'boolean':
        return (
          <>
            <Form.Item
              name={[field.name, 'value']}
              rules={[{ required: true, message: `${t('searchFilterBox.selectAnyOne')}` }]}
            >
              <Radio.Group>
                <Radio.Button value="true">Active</Radio.Button>
                <Radio.Button value="false">Inactive</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </>
        );
      default:
        return (
          <>
            <Form.Item className="mb-3">
              <Select
                disabled
                className="!w-[124px]"
                placeholder={`${t('searchFilterBox.selectField')}`}
                onClick={(e) => e.stopPropagation()}
              />
            </Form.Item>
            <Form.Item className="!w-[216px] mb-3">
              <Input disabled placeholder={`{${t('searchFilterBox.enterInput')}}`} />
            </Form.Item>
          </>
        );
    }
  };

  const handleFormApply = useCallback(
    (e: { filters: IAssignedFilters[] }) => {
      const appliedFilters = e.filters;

      onFilterApply({ filters: appliedFilters });
      setLastAppliedFilters(appliedFilters);
      setIsPopoverOpen(false);

      const matchingQuickFilter = quickFilter?.value?.find(
        (filter: IQuickFilterPayload) =>
          JSON.stringify(filter.filters) === JSON.stringify(appliedFilters)
      );

      if (!matchingQuickFilter) {
        emit(quickFilterTitleEventKey, { title: undefined });
      }

      if (isFilterName && isAppliedOnlyFilter) {
        const filterName = form.getFieldValue('filterName');
        setQuickFilters(filterName, appliedFilters);
        setIsFilterName(false);
        form.setFieldValue('filterName', '');
      }
    },
    [
      onFilterApply,
      quickFilter,
      quickFilterTitleEventKey,
      isFilterName,
      isAppliedOnlyFilter,
      setQuickFilters,
      form,
    ]
  );

  const handlePopoverOpen = (visible: boolean) => {
    setIsPopoverOpen(visible);

    if (visible && lastAppliedFilters.length > 0) {
      form.setFieldsValue({ filters: lastAppliedFilters });
      const mappedFields = lastAppliedFilters.map((filter) => {
        const fieldDef = mappedValues.find((col) => col.value === filter.field);
        return fieldDef
          ? {
              value: filter.field,
              label: filter.field,
              type: fieldDef.type,
            }
          : null;
      });
      setSelectedFields(mappedFields);
    } else if (lastAppliedFilters.length === 0) {
      form.resetFields();
    }
  };

  const handleClearAll = useCallback(() => {
    setSelectedQuickFilterData([]);
    setSelectedFields([]);
    setLastAppliedFilters([]);
    setIsFilterName(false);
    setIsPopoverOpen(false);

    setFilterParams((prev: PaginationParams & FilterParams) => ({
      pageNumber: prev.pageNumber,
      pageSize: prev.pageSize,
      searchTerm: prev.searchTerm,
      sortDirection: prev.sortDirection,
    }));

    emit(quickFilterTitleEventKey, {
      title: undefined,
    });
  }, [quickFilterTitleEventKey, setFilterParams, setSelectedQuickFilterData]);

  useEffect(() => {
    if (clearAllFunctionRef?.current) {
      clearAllFunctionRef.current.handleClearAll = handleClearAll;
    }
  }, [clearAllFunctionRef, handleClearAll]);

  useEffect(() => {
    if (form && quickFilter && quickFilterEventKey) {
      emit(quickFilterEventKey, {
        form,
        onFilterApply: handleFormApply,
        quickFilter,
        refetchFilters,
        handleClearAll,
      });
    }
  }, [form, handleClearAll, handleFormApply, quickFilter, quickFilterEventKey, refetchFilters]);

  const onSearchInputHandler = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchText && setSearchText(value);
    if (!value && onSearch) {
      setSearchText && setSearchText(value);
      setTimeout(() => onSearch(value), 1000);
    }
  };

  const onSelectChangeHandler = useCallback(
    (value: string, option: OptionsInterface[], index: number) => {
      if (!option) return;
      handleOnChange(value, option, index);
      form.setFieldsValue({
        filters: form
          .getFieldValue('filters')
          .map((item: IAssignedFilters, i: number) => (i === index ? { field: value } : item)),
      });
    },
    [form, handleOnChange]
  );

  const notificationManager = useNotificationManager();

  const addNewQuickFilter = async (filterName: string, filters: IAssignedFilters[]) => {
    try {
      await form.validateFields();
      setIsAppliedOnlyFilter(true);

      const modifiedFilters: IAssignedFilters[] = filters
        .map((filter) => {
          const matchedField = mappedValues.find((m) => m.value === filter.field);
          if (matchedField) {
            return { ...filter, type: matchedField.type };
          }
          return null;
        })
        .filter(Boolean) as IAssignedFilters[];

      const sanitizeFilterName = filterName.trimStart().trimEnd();

      const newQuickFilter = {
        name: sanitizeFilterName,
        filters: modifiedFilters,
      };

      const updatedQuickFilterPayload = {
        value: [...quickFilter.value, newQuickFilter],
      };
      const isFIlterAlreadyExist = quickFilter.value?.find(
        (filter: IQuickFilterPayload) => filter.name === sanitizeFilterName
      );

      if (isFIlterAlreadyExist) {
        notificationManager.error({
          message: t('common.error'),
          description: t('quickFilter.quickFilterExists'),
        });
        return;
      }
      await updateSetting(quickFilter.id, updatedQuickFilterPayload);

      notificationManager.success({
        message: t('common.success'),
        description: t('quickFilter.quickFilterAddedSuccessfully'),
      });

      await refetchFilters();
      handleFormApply({ filters });
      setIsFilterName(false);
      form.resetFields(['filterName']);
      emit(quickFilterTitleEventKey, { title: filterName });
    } catch (error: unknown) {
      const isFieldError = (error as { errorFields?: unknown })?.errorFields;

      notificationManager.error({
        message: t('common.error'),
        description: isFieldError ? t('quickFilter.emptyFieldError') : t('common.somethingWrong'),
      });
    }
  };

  return (
    <div className={`pt-5 ${className}`}>
      <div className="flex gap-3">
        <Search
          className="search-filter-box rounded-[8px] 3xsm:w-full lg:w-full lg:max-w-[300px]"
          placeholder={searchInputPlaceholder || 'Search text'}
          onInput={onSearchInputHandler}
          value={searchText}
          onSearch={onSearch}
          onClear={() => setSearchText && setSearchText('')}
          allowClear
        />
        {advanceFilter && (
          <Popover
            onOpenChange={handlePopoverOpen}
            forceRender
            open={isPopoverOpen}
            className="search-filter-popover"
            placement={window.screen.width < 769 ? 'bottom' : 'bottomRight'}
            content={
              <div
                ref={filterRef}
                className="w-[230px] md:w-full lg:w-full max-w-[640px] bg-white rounded-lg p-1"
              >
                <Form
                  scrollToFirstError
                  form={form}
                  name="advanced_filter"
                  onFinish={(e) => {
                    handleFormApply(e);
                  }}
                  autoComplete="off"
                  initialValues={{
                    filters: [
                      {
                        field: mappedValues[0]?.value,
                      },
                    ],
                  }}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex gap-2">
                      <h4 className="text-sm font-semibold text-justify">{`${t('searchFilterBox.advancedFilter')}`}</h4>
                      <CustomTooltip content="Select the fields from below dropdown you want to filter">
                        <img src={infoCircleOutlined} alt="info" />
                      </CustomTooltip>
                    </div>
                    <div className="flex flex-col gap-1">
                      <p className="font-semibold text-justify">{`${t('searchFilterBox.selectFields')}`}</p>
                      <div className="max-h-[350px] overflow-y-auto overflow-x-hidden">
                        <Form.List name="filters">
                          {(fields, { add, remove }) => (
                            <div className="pt-2">
                              {fields?.map((field, index) => (
                                <Space
                                  key={field.key}
                                  align="start"
                                  className="w-full mb-0 grid grid-cols-[1fr_1fr_1fr_35px]"
                                >
                                  <Form.Item
                                    {...field}
                                    name={[field.name, 'field']}
                                    rules={[
                                      { required: true, message: t('ordersPage.missingField') },
                                    ]}
                                    className="mb-3 w-full min-w-[200px]"
                                  >
                                    <Select
                                      placeholder="Select an option"
                                      options={mappedValues}
                                      onClick={(e) => e.stopPropagation()}
                                      onChange={(value, option) =>
                                        onSelectChangeHandler(
                                          value,
                                          option as OptionsInterface[],
                                          index
                                        )
                                      }
                                    />
                                  </Form.Item>
                                  {renderFields(field, index)}
                                  {index > 0 && (
                                    <Button
                                      icon={<FilterBoxDeleteOutlinedIcon />}
                                      onClick={() => {
                                        remove(field.name);
                                        const newSelectedFields = [...selectedFields];
                                        newSelectedFields.splice(index, 1);
                                        setSelectedFields(newSelectedFields);
                                      }}
                                      className="text-red-500 pt-2 cursor-pointer hover:!border-gray-300"
                                    />
                                  )}
                                </Space>
                              ))}
                              <div className="flex justify-between">
                                <Form.Item className="w-[45%] md:w-[30%] lg:w-[18%] shadow-none p-0 mb-0">
                                  <Button
                                    onClick={() => {
                                      add();
                                      setSelectedFields([...selectedFields, null]);
                                    }}
                                    block
                                    icon={<FilterBoxPlusOutlinedIcon />}
                                    className="text-primary-600 hover:!text-primary-600  transition-none font-normal border-none shadow-none p-0 justify-start"
                                  >
                                    {t('searchFilterBox.addFilter')}
                                  </Button>
                                </Form.Item>
                                {isSetQuickFilter && (
                                  <Form.Item className="w-[55%] md:w-[30%] lg:w-[28%] shadow-none p-0 mb-0">
                                    <Button
                                      block
                                      icon={<FilterBoxPlusOutlinedIcon />}
                                      className="text-primary-600 transition-none hover:!text-primary-600 font-normal border-none shadow-none p-0 justify-start"
                                      onClick={() => setIsFilterName(true)}
                                    >
                                      {t('searchFilterBox.setAsQuickFilter')}
                                    </Button>
                                  </Form.Item>
                                )}
                              </div>
                            </div>
                          )}
                        </Form.List>
                        {isFilterName && (
                          <div className="flex w-full items:center gap-3 py-1">
                            <Form.Item
                              rules={[
                                { required: true, message: t('ordersPage.missingField') },
                                { max: 100, message: t('priceModifiers.maximumValueExceeded') },
                                {
                                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                                  message: `${t('searchFilterBox.noMultiSpaces')}`,
                                },
                              ]}
                              name="filterName"
                              className="w-[84%] mb-0"
                            >
                              <Input className="!h-[40px]" type="text" />
                            </Form.Item>
                            <Form.Item className="px-3 w-[75px] mb-0 ">
                              <Button
                                type="primary"
                                className="!h-[40px] bg-primary-600 text-white hover:!bg-primary-600 hover:!text-white hover:!border-gray-500"
                                onClick={() =>
                                  addNewQuickFilter(
                                    form.getFieldValue('filterName'),
                                    form.getFieldsValue()?.filters
                                  )
                                }
                              >
                                {t('common.save')}
                              </Button>
                            </Form.Item>
                          </div>
                        )}
                      </div>
                      <Space className="w-full justify-end mt-4">
                        <Form.Item className="mb-0">
                          <Button
                            onClick={() => {
                              handleClearAll();
                            }}
                            className="bg-white text-gray-600 border border-gray-300 hover:bg-gray-50 hover:!border-gray-300 hover:!text-gray-700"
                          >
                            {t('searchFilterBox.clearAll')}
                          </Button>
                        </Form.Item>
                        <Form.Item className="mb-0">
                          <Button
                            type="primary"
                            htmlType="submit"
                            className="bg-primary-600 hover:!bg-primary-600 border-primary-bg-primary-600 text-white hover:!text-white hover:!border-primary-bg-primary-600 font-semibold h-auto"
                          >
                            {t('searchFilterBox.apply')}
                          </Button>
                        </Form.Item>
                      </Space>
                    </div>
                  </div>
                </Form>
              </div>
            }
            trigger="click"
          >
            <Button
              className={`h-[40px] !w-[40px] ${isFilterOpen ? 'border-[1px] border-primary-600' : ''}`}
              icon={<FilterInputIcon bool={isFilterOpen} />}
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            />
          </Popover>
        )}
      </div>
    </div>
  );
};

export default memo(SearchFilterComponent);
