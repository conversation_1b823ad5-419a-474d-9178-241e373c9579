@import '../../node_modules/ag-grid-community/styles/ag-grid.css';
@import './theme.css';

#gridWrapper {
  height: 100%;
  width: 100%;
}
#gridWrapperForChildren {
  height: 79vh;
  width: 100%;
}
#gridWrapperForGroupModifier {
  height: 67vh;
  width: 100%;
}
#gridWrapperForExtraSmall {
  height: 56vh;
  width: 100%;
}
#gridWrapperForGroupModifierChildren {
  height: 60vh;
  width: 100%;
}

@media screen and (max-width: 768px) {
  #gridWrapperForChildren {
    height: 100%;
    max-height: 76vh;
  }

  #gridWrapper {
    height: 100%;
  }
}
.ag-root-wrapper {
  border: none;
}

.ag-theme-alpine {
  --ag-checkbox-border-radius: 8px;
  --ag-checkbox-checked-color: var(--primary-600);
  --ag-checkbox-background-color: white;
  --ag-checkbox-indeterminate-color: var(--primary-600);
  --ag-foreground-color: #20363f;
  --ag-checkbox-unchecked-color: var(--primary-600);
  --ag-selected-row-background-color: transparent; /* selected rows in row selection  */
  --ag-background-color: var(--white); /* background color of grid*/
  --ag-odd-row-background-color: var(--primary-25); /* row color of grid in odd sequence*/
  --ag-header-foreground-color: #20363f; /* header text */
  --ag-header-background-color: var(--primary-50); /* heder background color*/
  --ag-font-size: 14px !important;
  --ag-font-family: var(--font-family);
  --ag-border-color: #bfc2df;
  --ag-alpine-active-color: var(--primary-600);
  --ag-range-selection-border-color: var(--primary-600);
  --ag-row-hover-color: var(--primary-600);
  --ag-header-column-separator-display: block;
  --ag-header-column-separator-width: 1px;
  --ag-header-column-separator-height: 100%;
  --ag-header-column-separator-color: #bfc2df;
  --ag-cell-horizontal-border: 1px solid var(--primary-100);
  --ag-row-border-color: var(--primary-100);
  --ag-row-border-width: 1px;
  --ag-header-height: 40px;
  --ag-icon-size: 16px;
  --ag-tooltip-background-color: var(--primary-600);
  --ag-header-cell-moving-background-color: var(--primary-50);
  --ag-row-height: 30px;
}

.ag-paging-panel {
  background-color: var(--primary-50);
  height: 48px;
}

.ag-tooltip {
  color: #ffffff;
  border-radius: 4px;
}

.ag-popup {
  border-radius: 2px;
}

.ag-row-hover {
  color: #ffffff;
}

/* .cell-icons and .cell-icons-stroke class will applied on every cell icon (depends on SVG)*/
.ag-row-hover .cell-icons {
  fill: white;
}

.ag-row-hover .cell-icons-stroke {
  stroke: white;
}

/* pinned heder shadow*/
.ag-pinned-right-header {
  box-shadow: -13px 5px 28px -5px #00000026;
}

/* pinned column shadow*/
.ag-pinned-right-cols-container {
  box-shadow: -4px 1px 15px 0px #00000026;
}

.ag-paging-panel > .ag-paging-page-size .ag-wrapper {
  display: flex;
  gap: 5px;
  border-radius: 8px;
  height: 30px;
}

.ag-select-list-item:hover {
  color: var(--white) !important;
}

.ag-popup-child {
  border-radius: 4px;
}

.ag-select-list .ag-active-item {
  color: var(--white) !important;
}
.ag-header-cell-label {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.ag-ltr .ag-cell {
  font-size: 14px !important;
}

.ag-icon::before {
  color: #20363f;
}
