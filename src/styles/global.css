@tailwind base;
@tailwind components;
@tailwind utilities;

/* load the font family */
@font-face {
  font-family: 'Inter';
  src: url('../assets/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

* {
  font-family: 'Inter';
}

#root {
  height: 100vh;
  /* overflow: hidden; */
}

.pac-container {
  z-index: 10000 !important;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
  z-index: 99999;
}

::-webkit-scrollbar-track {
  width: 6px;
  height: 6px;
  background-color: var(--primary-25);
}

::-webkit-scrollbar-thumb {
  width: 6px;
  height: 6px;
  border-radius: 4px;
  background-color: var(--primary-200);
}

.filter-popup::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 15px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

.filter-popup {
  --tw-shadow: 0 10px 37px 15px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.ant-picker-outlined:hover {
  border: 1px solid #4096ff !important;
}

.ant-picker-active-bar {
  background: #4096ff !important;
}

.ant-picker-cell-range-start > .ant-picker-cell-inner {
  background: var(--primary-500) !important;
}

.ant-picker-cell-range-end > .ant-picker-cell-inner {
  background: var(--primary-500) !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.ant-select-outlined {
  margin: 0px !important;
}

.error-chip {
  @apply text-error-600 border border-error-600 bg-error-25 px-2 py-[2px] rounded-md leading-5 text-center;
}

.warning-chip {
  @apply text-warning-600 border border-warning-600 bg-warning-25 px-2 py-[2px] rounded-md leading-5 text-center;
}

.success-chip {
  @apply text-success-600 border border-success-600 bg-success-25 px-2 py-[2px] rounded-md leading-5 text-center;
}

.grey-chip {
  @apply text-grey-600 border border-grey-600 bg-grey-25 px-2 py-[2px] rounded-md leading-5 text-center;
}

.primary-chip {
  @apply text-primary-600 border border-primary-600 bg-primary-25 px-2 py-[2px] rounded-md leading-5 text-center;
}
