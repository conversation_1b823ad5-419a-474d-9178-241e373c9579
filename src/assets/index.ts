import closeNotifyIcon from './svgs/closeNotifyIcon.svg';
import successNotifyIconSvg from './svgs/successNotifyIcon.svg';
import errorNotifyIconSvg from './svgs/errorNotifyIcon.svg';
import infoNotifyIconSvg from './svgs/infoNotifyIcon.svg';
import warnNotifyIconSvg from './svgs/warnNotifyIcon.svg';
import subMenuSvg from './svgs/subMenu.svg';
import backArrowIconsvg from './svgs/backArrowIcon.svg';
import infoCircleOutlined from './svgs/infoCircleIcon.svg';
import deleteSvg from './svgs/delete.svg';
import modalWarningIcon from './svgs/modalWarningIcon.svg';
import modalSuccess from './svgs/modalSuccessIcon.svg';
import modalError from './svgs/modalErrorIcon.svg';
import modalDelete from './svgs/modalDeleteIcon.svg';
import Logo from './svgs/logo.svg';
import LogoIcon from './svgs/logoIcon.svg';
import LogoutIcon from './svgs/logoutIcon.svg';
import emptyStateIcon from './svgs/emptyState.svg';
import emptyStateIcon2 from './svgs/emptyState2.svg';
import mobileSmsIcon from './svgs/mobileSmsIcon.svg';
import emailIcon from './svgs/emailIcon.svg';
import notificationPushIcon from './svgs/notificationPushIcon.svg';
import dotPng from './png/dot.png';
import InfoForPopupIcon from './svgs/infoForPopupIcon.svg';
import FlashFeaturesIcon from './svgs/flashFeaturesIcon.svg';
import ArrowSvgIcon from './svgs/arrowSvgIcon.svg';
import FirstPageButtonIcon from './svgs/firstPageButton.svg';
import PreviousPageButtonIcon from './svgs/previousPageButtonIcon.svg';
import NextPageButtonIcon from './svgs/nextPageButtonIcon.svg';
import LastPageButtonIcon from './svgs/lastPageButtonIcon.svg';
import ArrowDownIcon from './svgs/arrowDownIcon.svg';
import LockIcon from './svgs/lockIcon.svg';
import ClearCloseIcon from './svgs/clearCloseIcon.svg';
import RightArrowIcon from './svgs/rightArrowIcon.svg';
import LumigoLogo from './svgs/lumigologo.svg';
import InvoiceTemplateShadder from './png/invoice-bg-shadder.png';

export { UpdateStatusIcon } from './icons/updateStatus';
export { AssignToOutlined } from './icons/assignToOutlined';
export { ColumnManageMenuIcon } from './icons/columnManageMenuIcon';
export { ColumnMergeIcon } from './icons/columnMergeIcon';
export { CustomerSelectedOutlinedIcon } from './icons/customerSelectedOutlined';
export { CustomerIcon } from './icons/customersOutlined';
export { DuplicateCustomerIcon } from './icons/duplicateCustomerIcon';
export { EyeIcon } from './icons/eyeIconSvg';
export { FilterBoxDeleteOutlinedIcon } from './icons/filterBoxDeleteOutlined';
export { FilterBoxPlusOutlinedIcon } from './icons/filterBoxPlusOutlined';
export { FilterInputIcon } from './icons/filterInputIcon';
export { GridPagingIcon } from './icons/gridPagingIcon';
export { HistoryIcon } from './icons/historyIcon';
export { LeftOutlinedIcon } from './icons/leftOutlinedIcon';
export { LocationOutlinedIcon } from './icons/locationOutlinedIcon';
export { LocationSelectedOutlinedIcon } from './icons/locationSelectedOutlined';
export { NotificationBellIcon } from './icons/notificationBellIcon';
export { PlusButtonIcon } from './icons/plusButtonIcon';
export { PricesOutlinedIcon } from './icons/pricesOutlined';
export { PricesSelectedOutlinedIcon } from './icons/pricesSelectedOutlined';
export { PrinterIcon } from './icons/printerIcon';
export { RightOutlined } from './icons/rightOutlinedIcon';
export { SearchInputIcon } from './icons/searchInputIcon';
export { SettingSelectedIcon } from './icons/settingsSelectedOutlined';
export { SettingIcon } from './icons/settingsOutlined';
export { TruckOutlinedIcon } from './icons/truckOutlined';
export { TruckSelectedOutlinedIcon } from './icons/truckSelectedOutlined';
export { UnAssignOutlined } from './icons/unAssignOutlined';
export { DeleteIcon } from './icons/deleteIcon';
export { ExpandCollapseIcon } from './icons/expandCollapseIcon';
export { ExpandedDownCollapseIcon } from './icons/expandedDownCollapseIcon';
export { EmailCredentialIcon } from './icons/EmailCredentialIcon';
export { AssignToIcon } from './icons/assignToIcon';
export { LeftToRightOutlinedIcon } from './icons/leftToRightOutlined';
export { RightToLeftOutlined } from './icons/rightToLeftOutlined';
export { ModifierIcon } from './icons/ModifierIcon';
export { ModifierGroupIcon } from './icons/modifierGroupIcon';
export { ModifierGroupGridIcon } from './icons/modifierGroupGridIcon';
export { ModifierGridIcon } from './icons/modifierGridIcon';
export { PlusModifierIcon } from './icons/PlusModifierIcon';
export { SelectSuffixIcon } from './icons/SelectSuffixIcon';
export { OrdersWarningIcon } from './icons/ordersWarningIcon';
export { CollapseDownIcon } from './icons/collapseDownIcon';
export { CollapseUpIcon } from './icons/collapseUpIcon';
export { EditPopupIcon } from './icons/editPopupIcon';
export { DateCalendarIcon } from './icons/dateCalendarOutlined';
export { OrderEntryIcon } from './icons/orderEntryIcon';
export { OrderEntrySelectedIcon } from './icons/orderEntrySelectedIcon';
export { OrderListIcon } from './icons/orderListIcon';
export { AddressesIcon } from './icons/addressesIcon';
export { InvoicesIcon } from './icons/invoicesIcon';
export { ContactsIcon } from './icons/contactsIcon';
export { PhoneOutlinedIcon } from './icons/phoneOutlinedIcon';
export { EmailOutlinedIcon } from './icons/emailOutlinedIcon';
export { SearchForSelectIcon } from './icons/SearchForSelectIcon';
export { StarForSelectFilledIcon } from './icons/StartForSelectFilled';
export { StarForSelectUnfilledIcon } from './icons/StarForSelectUnfilledIcon';
export {
  //login
  dotPng,
  // Notification
  successNotifyIconSvg,
  errorNotifyIconSvg,
  infoNotifyIconSvg,
  warnNotifyIconSvg,
  closeNotifyIcon,
  // context Menu
  subMenuSvg,
  //sidebar,
  backArrowIconsvg,
  infoCircleOutlined,
  deleteSvg,
  modalWarningIcon,
  modalSuccess,
  modalError,
  modalDelete,
  Logo,
  LogoIcon,
  LogoutIcon,
  emptyStateIcon,
  mobileSmsIcon,
  emailIcon,
  notificationPushIcon,
  InfoForPopupIcon,
  FlashFeaturesIcon,
  ArrowSvgIcon,
  emptyStateIcon2,
  FirstPageButtonIcon,
  PreviousPageButtonIcon,
  NextPageButtonIcon,
  LastPageButtonIcon,
  ArrowDownIcon,
  LockIcon,
  ClearCloseIcon,
  RightArrowIcon,
  LumigoLogo,
  InvoiceTemplateShadder,
};
