import { PRIMARY } from '@/styles/colorConstants';

export const ContactsIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill={bool ? PRIMARY[600] : '#20363F'}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.86968 8.715C6.84718 8.715 6.83218 8.715 6.80968 8.715C6.77218 8.7075 6.71968 8.7075 6.67468 8.715C4.49968 8.6475 2.85718 6.9375 2.85718 4.83C2.85718 2.685 4.60468 0.9375 6.74968 0.9375C8.89468 0.9375 10.6422 2.685 10.6422 4.83C10.6347 6.9375 8.98468 8.6475 6.89218 8.715C6.88468 8.715 6.87718 8.715 6.86968 8.715ZM6.74968 2.0625C5.22718 2.0625 3.98218 3.3075 3.98218 4.83C3.98218 6.33 5.15218 7.5375 6.64468 7.59C6.68968 7.5825 6.78718 7.5825 6.88468 7.59C8.35468 7.5225 9.50968 6.315 9.51718 4.83C9.51718 3.3075 8.27218 2.0625 6.74968 2.0625Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M12.4047 8.8125C12.3822 8.8125 12.3597 8.8125 12.3372 8.805C12.0297 8.835 11.7147 8.6175 11.6847 8.31C11.6547 8.0025 11.8422 7.725 12.1497 7.6875C12.2397 7.68 12.3372 7.68 12.4197 7.68C13.5147 7.62 14.3697 6.72 14.3697 5.6175C14.3697 4.4775 13.4472 3.555 12.3072 3.555C11.9997 3.5625 11.7447 3.3075 11.7447 3C11.7447 2.6925 11.9997 2.4375 12.3072 2.4375C14.0622 2.4375 15.4947 3.87 15.4947 5.625C15.4947 7.35 14.1447 8.745 12.4272 8.8125C12.4197 8.8125 12.4122 8.8125 12.4047 8.8125Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M6.87721 16.9125C5.40721 16.9125 3.92971 16.5375 2.81221 15.7875C1.76971 15.0975 1.19971 14.1525 1.19971 13.125C1.19971 12.0975 1.76971 11.145 2.81221 10.4475C5.06221 8.955 8.70721 8.955 10.9422 10.4475C11.9772 11.1375 12.5547 12.0825 12.5547 13.11C12.5547 14.1375 11.9847 15.09 10.9422 15.7875C9.81721 16.5375 8.34721 16.9125 6.87721 16.9125ZM3.43471 11.3925C2.71471 11.8725 2.32471 12.4875 2.32471 13.1325C2.32471 13.77 2.72221 14.385 3.43471 14.8575C5.30221 16.11 8.45221 16.11 10.3197 14.8575C11.0397 14.3775 11.4297 13.7625 11.4297 13.1175C11.4297 12.48 11.0322 11.865 10.3197 11.3925C8.45221 10.1475 5.30221 10.1475 3.43471 11.3925Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M13.7548 15.5625C13.4923 15.5625 13.2598 15.3825 13.2073 15.1125C13.1473 14.805 13.3423 14.5125 13.6423 14.445C14.1148 14.3475 14.5498 14.16 14.8873 13.8975C15.3148 13.575 15.5473 13.17 15.5473 12.7425C15.5473 12.315 15.3148 11.91 14.8948 11.595C14.5648 11.34 14.1523 11.16 13.6648 11.0475C13.3648 10.98 13.1698 10.68 13.2373 10.3725C13.3048 10.0725 13.6048 9.8775 13.9123 9.945C14.5573 10.0875 15.1198 10.3425 15.5773 10.695C16.2748 11.22 16.6723 11.9625 16.6723 12.7425C16.6723 13.5225 16.2673 14.265 15.5698 14.7975C15.1048 15.1575 14.5198 15.42 13.8748 15.5475C13.8298 15.5625 13.7923 15.5625 13.7548 15.5625Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
    </svg>
  );
};
