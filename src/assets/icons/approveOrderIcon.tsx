import { PRIMARY } from '@/styles/colorConstants';

export const ApproveOrderIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 1.4375C13.1714 1.4375 16.5625 4.82864 16.5625 9C16.5625 13.1714 13.1714 16.5625 9 16.5625C4.82864 16.5625 1.4375 13.1714 1.4375 9C1.4375 4.82864 4.82864 1.4375 9 1.4375ZM9 1.5625C4.89886 1.5625 1.5625 4.89886 1.5625 9C1.5625 13.1011 4.89886 16.4375 9 16.4375C13.1011 16.4375 16.4375 13.1011 16.4375 9C16.4375 4.89886 13.1011 1.5625 9 1.5625Z"
        stroke={bool ? PRIMARY[600] : '#090A1A'}
      />
      <path
        d="M12.2138 6.81445L12.2314 6.82617C12.2408 6.8356 12.248 6.85035 12.248 6.87012C12.248 6.88023 12.2462 6.88906 12.2431 6.89648L12.2314 6.91406L7.97943 11.167C7.96725 11.1792 7.95121 11.1854 7.93549 11.1855C7.92767 11.1855 7.91941 11.1837 7.91205 11.1807L7.89154 11.167L5.76849 9.04395C5.75912 9.0345 5.75189 9.01971 5.75189 9C5.75192 8.99027 5.75386 8.98185 5.75677 8.97461L5.76849 8.95605C5.77793 8.94665 5.79271 8.93947 5.81244 8.93945C5.8225 8.93945 5.8314 8.94126 5.83881 8.94434L5.85638 8.95605L7.58099 10.6816L7.93549 11.0352L8.289 10.6816L12.1435 6.82617C12.1529 6.81674 12.1677 6.80959 12.1874 6.80957C12.1975 6.80957 12.2064 6.81137 12.2138 6.81445Z"
        fill={bool ? PRIMARY[600] : '#090A1A'}
        stroke={bool ? PRIMARY[600] : '#090A1A'}
      />
    </svg>
  );
};
