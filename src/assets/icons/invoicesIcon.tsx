import { PRIMARY } from '@/styles/colorConstants';

export const InvoicesIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill={bool ? PRIMARY[600] : '#20363F'}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.565 8.8125H12C11.6925 8.8125 11.4375 8.5575 11.4375 8.25V3.0075C11.4375 2.4525 11.655 1.935 12.045 1.545C12.435 1.155 12.9525 0.9375 13.5075 0.9375H13.515C14.4525 0.945 15.3375 1.3125 16.0125 1.98C16.6875 2.6625 17.055 3.5625 17.055 4.5V6.315C17.0625 7.8075 16.0575 8.8125 14.565 8.8125ZM12.5625 7.6875H14.565C15.435 7.6875 15.9375 7.185 15.9375 6.315V4.5C15.9375 3.855 15.6825 3.24 15.225 2.775C14.7675 2.325 14.1525 2.07 13.515 2.0625C13.515 2.0625 13.515 2.0625 13.5075 2.0625C13.26 2.0625 13.02 2.16 12.84 2.34C12.66 2.52 12.5625 2.7525 12.5625 3.0075V7.6875Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M6.75 17.4975C6.3975 17.4975 6.0675 17.3625 5.82 17.1075L4.575 15.855C4.5075 15.7875 4.4025 15.78 4.3275 15.84L3.0375 16.8C2.64 17.1 2.115 17.1525 1.665 16.9275C1.215 16.7025 0.9375 16.2525 0.9375 15.75V4.5C0.9375 2.235 2.235 0.9375 4.5 0.9375H13.5C13.8075 0.9375 14.0625 1.1925 14.0625 1.5C14.0625 1.8075 13.8075 2.0625 13.5 2.0625C12.9825 2.0625 12.5625 2.4825 12.5625 3V15.75C12.5625 16.2525 12.285 16.7025 11.835 16.9275C11.385 17.1525 10.86 17.1075 10.4625 16.8075L9.18 15.8475C9.105 15.7875 9 15.8025 8.94 15.8625L7.68 17.1225C7.4325 17.3625 7.1025 17.4975 6.75 17.4975ZM4.4325 14.6775C4.7775 14.6775 5.115 14.805 5.37 15.0675L6.615 16.32C6.66 16.365 6.72 16.3725 6.75 16.3725C6.78 16.3725 6.84 16.365 6.885 16.32L8.145 15.06C8.61 14.595 9.345 14.55 9.8625 14.9475L11.1375 15.9C11.22 15.96 11.295 15.9375 11.3325 15.915C11.37 15.8925 11.4375 15.8475 11.4375 15.75V3C11.4375 2.6625 11.52 2.34 11.6625 2.0625H4.5C2.835 2.0625 2.0625 2.835 2.0625 4.5V15.75C2.0625 15.855 2.13 15.9 2.1675 15.9225C2.2125 15.945 2.2875 15.96 2.3625 15.9L3.645 14.94C3.8775 14.7675 4.155 14.6775 4.4325 14.6775Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M9 10.32H6.75C6.4425 10.32 6.1875 10.065 6.1875 9.75751C6.1875 9.45001 6.4425 9.19501 6.75 9.19501H9C9.3075 9.19501 9.5625 9.45001 9.5625 9.75751C9.5625 10.065 9.3075 10.32 9 10.32Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M9 7.32001H6.75C6.4425 7.32001 6.1875 7.06501 6.1875 6.75751C6.1875 6.45001 6.4425 6.19501 6.75 6.19501H9C9.3075 6.19501 9.5625 6.45001 9.5625 6.75751C9.5625 7.06501 9.3075 7.32001 9 7.32001Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M4.47766 7.50751C4.06516 7.50751 3.72766 7.17001 3.72766 6.75751C3.72766 6.34501 4.06516 6.00751 4.47766 6.00751C4.89016 6.00751 5.22766 6.34501 5.22766 6.75751C5.22766 7.17001 4.89016 7.50751 4.47766 7.50751Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
      <path
        d="M4.47766 10.5075C4.06516 10.5075 3.72766 10.17 3.72766 9.75751C3.72766 9.34501 4.06516 9.00751 4.47766 9.00751C4.89016 9.00751 5.22766 9.34501 5.22766 9.75751C5.22766 10.17 4.89016 10.5075 4.47766 10.5075Z"
        fill={bool ? PRIMARY[600] : '#20363F'}
      />
    </svg>
  );
};
