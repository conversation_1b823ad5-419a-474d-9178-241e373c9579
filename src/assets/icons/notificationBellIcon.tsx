import { PRIMARY } from '@/styles/colorConstants';

export const NotificationBellIcon = ({ bool }: { bool: boolean }) => {
  const currentColor = bool ? PRIMARY[600] : '#20363F';

  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1943_10723)">
        <path
          d="M3.0123 13.1566L3.01233 13.1566L3.01032 13.1512C2.85249 12.7322 2.89843 12.2544 3.17265 11.8013L3.17325 11.8003L4.03575 10.3678L4.03615 10.3672C4.15891 10.1626 4.25976 9.88928 4.32947 9.63609C4.39925 9.38261 4.4524 9.09627 4.4524 8.85744V6.68994C4.4524 4.17608 6.50104 2.12744 9.0149 2.12744C11.5288 2.12744 13.5774 4.17608 13.5774 6.68994V8.85744C13.5774 9.09287 13.6307 9.37929 13.7001 9.63323C13.7696 9.88774 13.8698 10.1632 13.9909 10.37L13.9909 10.37L13.9937 10.3747L14.8481 11.7988C14.8482 11.7989 14.8483 11.799 14.8483 11.7991C15.0928 12.2088 15.1388 12.7035 14.973 13.1551C14.8089 13.6024 14.4529 13.9421 14.0036 14.09L14.0036 14.09L14.0005 14.091C12.4035 14.6282 10.7099 14.8974 9.0149 14.8974C7.32078 14.8974 5.62779 14.6285 4.02345 14.0916C3.51823 13.9165 3.16411 13.5741 3.0123 13.1566ZM3.28405 11.862L3.28404 11.862L3.28263 11.8644C3.06434 12.2315 2.96639 12.6843 3.13142 13.1123C3.28706 13.5436 3.65256 13.8262 4.06083 13.9656L4.06361 13.9666C7.30162 15.0511 10.7356 15.0511 13.9736 13.9666C14.387 13.8286 14.7127 13.515 14.8627 13.0998C15.0086 12.6956 14.9775 12.2449 14.7454 11.8615C14.7453 11.8613 14.7451 11.861 14.745 11.8608L13.885 10.4325C13.6449 10.0201 13.4524 9.30659 13.4524 8.84994V6.68994C13.4524 4.2388 11.466 2.25244 9.0149 2.25244C6.57188 2.25244 4.5774 4.23818 4.5774 6.68994V8.85744C4.5774 9.31276 4.37809 10.0338 4.14504 10.432L3.28405 11.862Z"
          stroke={currentColor}
        />
        <path
          d="M10.3893 2.45175L10.3893 2.45173L10.3849 2.45051C10.1498 2.38564 9.9198 2.33617 9.69525 2.30291L9.69526 2.30282L9.68615 2.30164C8.98416 2.2108 8.29821 2.25947 7.64887 2.45343L7.64884 2.45332L7.63897 2.4565C7.63098 2.45906 7.62249 2.45939 7.61289 2.4567C7.60255 2.4538 7.59266 2.44775 7.58524 2.43955C7.576 2.42934 7.57108 2.41827 7.56924 2.40834C7.56751 2.399 7.56817 2.38934 7.57252 2.37866L7.57257 2.37868L7.57523 2.37187C7.80811 1.77548 8.37373 1.38501 9.02197 1.38501C9.67089 1.38501 10.2331 1.76803 10.4664 2.36597C10.4786 2.40081 10.4697 2.42477 10.4613 2.43633C10.4566 2.44051 10.4502 2.44475 10.4417 2.44829C10.4304 2.45298 10.4187 2.45501 10.4095 2.45501C10.4053 2.45501 10.399 2.4545 10.3893 2.45175Z"
          fill={currentColor}
          stroke={currentColor}
        />
        <path
          d="M9.01562 16.6074C8.40653 16.6074 7.81348 16.3607 7.38168 15.9289C7.06964 15.6168 6.85424 15.2206 6.75875 14.7949H6.88694C6.98008 15.1873 7.18102 15.5524 7.46957 15.841C7.87811 16.2495 8.44019 16.4824 9.01562 16.4824C10.0498 16.4824 10.9187 15.761 11.1455 14.7949H11.2738C11.0451 15.8312 10.1203 16.6074 9.01562 16.6074Z"
          fill={currentColor}
          stroke={currentColor}
        />
      </g>
      <defs>
        <clipPath id="clip0_1943_10723">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
