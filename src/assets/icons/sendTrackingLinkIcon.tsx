import { PRIMARY } from '@/styles/colorConstants';

export const SendTrackingLinkIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.9041 2.55127C13.3117 2.07997 14.3624 2.22493 15.0047 2.75635L15.1277 2.86768C15.7126 3.4472 15.9257 4.45013 15.535 5.81201L15.4481 6.08838V6.08936L13.326 12.4565C12.8417 13.9057 12.3131 14.7255 11.8436 15.1782C11.3864 15.619 10.9753 15.7222 10.6649 15.7222C10.3544 15.7221 9.94403 15.6186 9.4881 15.1792C9.07823 14.7841 8.62327 14.1087 8.19415 12.978L8.01154 12.4644L7.3924 10.6069L7.15607 10.5278L5.53595 9.98779C4.09456 9.50733 3.27872 8.98063 2.82794 8.51221C2.38928 8.05632 2.28508 7.64606 2.28497 7.33545C2.28497 7.02909 2.38821 6.61731 2.82794 6.15869C3.27916 5.68821 4.09588 5.1583 5.53693 4.67432L5.53595 4.67334L11.9031 2.55225L11.9041 2.55127ZM13.6668 2.3999C13.2099 2.37074 12.7016 2.44055 12.1688 2.59619L11.9393 2.66846L5.57111 4.79834C4.49479 5.15842 3.72771 5.5631 3.21954 5.97412C2.72737 6.37227 2.40216 6.84206 2.40216 7.33545C2.40236 7.82884 2.72792 8.29841 3.22052 8.6958C3.72893 9.10587 4.49604 9.50758 5.57208 9.86377V9.86475L7.46173 10.4946L7.46857 10.4966C7.47127 10.4975 7.47809 10.5002 7.48517 10.5073L7.49591 10.5239L7.49786 10.5308L8.12775 12.4204C8.48421 13.4973 8.8892 14.2644 9.30157 14.7729C9.70115 15.2657 10.1722 15.5903 10.6649 15.5903C11.1583 15.5903 11.628 15.2652 12.0262 14.7729C12.3859 14.3282 12.74 13.685 13.0643 12.8101L13.202 12.4214V12.4204L15.324 6.05322L15.325 6.05225C15.5305 5.43176 15.6242 4.84007 15.5887 4.31689C15.5576 3.85945 15.4248 3.42943 15.1551 3.09229L15.0311 2.95361C14.6765 2.59912 14.1883 2.43319 13.6668 2.3999Z"
        stroke={bool ? PRIMARY[600] : '#090A1A'}
      />
      <path
        d="M10.2679 7.48438C10.2875 7.48449 10.3025 7.49161 10.3119 7.50098C10.3212 7.51041 10.3285 7.52529 10.3285 7.54492C10.3285 7.55494 10.3267 7.5639 10.3236 7.57129L10.3119 7.58887L10.3109 7.58984L7.62634 10.2822L7.61462 10.2939C7.61714 10.2913 7.61696 10.2924 7.61072 10.2949C7.60372 10.2977 7.5934 10.2998 7.5824 10.2998C7.57267 10.2998 7.56477 10.2982 7.55896 10.2959L7.53845 10.2812C7.52908 10.2718 7.52185 10.257 7.52185 10.2373C7.52187 10.2276 7.52381 10.2192 7.52673 10.2119L7.53845 10.1934H7.53943L10.224 7.50098C10.2335 7.49167 10.2483 7.48438 10.2679 7.48438Z"
        fill={bool ? PRIMARY[600] : '#090A1A'}
        stroke={bool ? PRIMARY[600] : '#090A1A'}
      />
    </svg>
  );
};
