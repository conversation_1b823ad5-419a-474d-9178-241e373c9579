export const isFormChangedHandler = (
  initialValue: Record<string, any>,
  currentValue: Record<string, any>,
  fieldsWithInitialValues: string[] = []
): boolean => {
  const isInitialValueEmpty = Object.keys(initialValue).length < 1;
  const isAddFormChanged = Object.keys(currentValue).some((bool) => {
    return !fieldsWithInitialValues.includes(bool) && currentValue[bool];
  });

  return isInitialValueEmpty
    ? isAddFormChanged
    : JSON.stringify(initialValue) !== JSON.stringify(currentValue);
};
