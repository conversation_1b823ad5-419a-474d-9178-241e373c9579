import { StorageKeys } from '@/types/enums/StorageEnums';
import { getParsedStorageItem, removeStorageItem } from '../Storage';
import { ROUTES } from '@/constant/RoutesConstant';
import { IUser } from '@/api/auth/auth.types';

export const getUserInfoFromStorage = (): IUser | null => {
  return getParsedStorageItem(StorageKeys.USER_INFO);
};

export const sessionExpiredHandler = () => {
  removeStorageItem(StorageKeys.USER_INFO);
  removeStorageItem(StorageKeys.IS_AUTHENTICATED);
  setTimeout(() => {
    window.location.replace(ROUTES.COMMON.LOGIN);
  }, 2000);
};
