import dayjs, { Dayjs } from 'dayjs';
import configManager from '../ConfigManager';

export const calculateTime = (startTime: dayjs.Dayjs, endTime: dayjs.Dayjs) => {
  const adjustedEnd = endTime.isBefore(startTime) ? endTime.add(1, 'day') : endTime;

  const duration = adjustedEnd.diff(startTime, 'minute');
  const totalHours = Math.floor(duration / 60);
  const totalMinutes = duration % 60;

  const timeString = `${totalHours}h ${totalMinutes}min`;

  return { totalHours, totalMinutes: timeString };
};

export const dateFormatter = (value: string, formatter?: string) => {
  const config = configManager.get('dateFormate');
  return value && dayjs(value).isValid() ? dayjs(value).format(formatter || config) : null;
};

export const convertInUTC = (value: Dayjs) => {
  return dayjs(value).utc().toISOString();
};
