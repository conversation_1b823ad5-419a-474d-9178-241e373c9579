export const DATABASE_NAME = 'transportAppConfig';
export const DATABASE_VERSION = 16;

interface StoreConfig {
  storeName: string;
  options?: IDBObjectStoreParameters;
  indexes?: { name: string; keyPath: string | string[]; options?: IDBIndexParameters }[];
}

export enum StoreName {
  Addresses = 'Addresses',
  Customers = 'Customers',
  Vehicles = 'Vehicles',
  VehicleTimeClockSessions = 'VehicleTimeClockSessions',
  Zone = 'Zone',
  zoneLookup = 'ZoneLookup',
  priceModifier = 'priceModifier',
  customerContact = 'customerContact',
  customerAddress = 'customerAddress',
  customerSettings = 'customerSettings',
  customerService = 'customerService',
  packageType = 'packageType',
  priceBreakdown = 'priceBreakdown',
}

export const STORES: StoreConfig[] = [
  {
    storeName: StoreName.Addresses,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.Zone,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.zoneLookup,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.priceModifier,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.Customers,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.Vehicles,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.customerContact,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.customerSettings,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.customerService,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.customerAddress,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.packageType,
    options: { keyPath: 'id' },
  },
  {
    storeName: StoreName.priceBreakdown,
    options: { keyPath: 'id' },
  },
];
