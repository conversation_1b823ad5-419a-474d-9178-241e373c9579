import { PRIMARY } from '@/styles/colorConstants';
import { ThemeConfig } from 'antd';

export const ANTD_THEME: ThemeConfig = {
  token: {
    fontFamily: 'Inter',
  },
  components: {
    Button: {
      colorPrimary: PRIMARY[600],
      colorPrimaryHover: PRIMARY[600],
      colorPrimaryActive: PRIMARY[600],
      colorPrimaryBg: PRIMARY[600],
      colorPrimaryBgHover: PRIMARY[600],
      borderRadius: 8,
    },
    Input: {
      colorPrimaryHover: PRIMARY[600],
      activeBorderColor: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
    },
    Select: {
      activeBorderColor: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
      optionSelectedColor: PRIMARY[600],
      optionSelectedFontWeight: 400,
      borderRadius: 8,
    },
    Tabs: {
      itemHoverColor: PRIMARY[600],
    },
    DatePicker: {
      colorPrimary: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
    },
    Checkbox: {
      colorPrimary: PRIMARY[600],
    },
    Radio: {
      colorPrimary: PRIMARY[600],
      buttonSolidCheckedBg: PRIMARY[600],
      buttonSolidCheckedHoverBg: PRIMARY[500],
    },
    Switch: {
      colorPrimary: PRIMARY[600],
      colorPrimaryHover: PRIMARY[400],
      colorTextQuaternary: PRIMARY[100],
      colorTextTertiary: PRIMARY[100],
      trackHeightSM: 20,
      trackMinWidthSM: 36,
      handleSizeSM: 16,
    },
    Steps: {
      colorPrimary: PRIMARY[600],
      colorPrimaryBorder: PRIMARY[100],
    },
  },
};
