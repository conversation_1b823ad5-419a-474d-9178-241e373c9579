import dayjs, { Dayjs, isDayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { convertInUTC } from './helper/dateHelper';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';

dayjs.extend(utc);

export const TransformFilters = (
  filters: Array<{ field: string; operator?: string; value: string }>
): Record<string, unknown> =>
  filters?.reduce(
    (result, filter) => {
      const key = filter.field.toLowerCase();
      result[key] = filter.operator
        ? { operator: filter.operator, value: filter.value }
        : filter.value === 'true';
      return result;
    },
    {} as Record<string, unknown>
  );

export const escapeRegex = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

export const highlightText = (value: string, search: string) => {
  if (!search) return value;
  const escapedSearch = escapeRegex(search);
  const regex = new RegExp(`(${escapedSearch})`, 'gi');

  const parts = String(value || '').split(regex);

  return (
    <span
      dangerouslySetInnerHTML={{
        __html: parts.map((part) => (regex.test(part) ? `<mark>${part}</mark>` : part)).join(''),
      }}
    />
  );
};

export const advanceFilterObjectMapper = async (
  filterObject: IAssignedFilters[]
): Promise<{ [key: string]: string }> => {
  const queryParams = filterObject.reduce(
    (acc, { field, operator, value }) => {
      value = isDayjs(value) ? convertInUTC(value as Dayjs) : value;
      value = Array.isArray(value)
        ? `${convertInUTC(dayjs(value[0]))},${convertInUTC(dayjs(value[0]))}`
        : value;
      acc[`${field}:${operator}`] = value;
      return acc;
    },
    {} as { [key: string]: string }
  );
  return queryParams;
};

export const maskQuickFilterData = (filters: IAssignedFilters[]) => {
  const quickFilterData = filters?.map((filter) => {
    if (isDayjs(filter.value)) {
      return {
        ...filter,
        value: filter.value.format('DD-MM-YYYY'),
      };
    } else if (Array.isArray(filter.value)) {
      return {
        ...filter,
        value: [
          dayjs(filter.value[0]).format('DD-MM-YYYY'),
          dayjs(filter.value[1]).format('DD-MM-YYYY'),
        ],
      };
    } else {
      return filter;
    }
  });
  return quickFilterData;
};
