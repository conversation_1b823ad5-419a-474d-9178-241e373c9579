import { CreateSettingsDto } from '@/api/settings/settings.types';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import { IQuickFilterPayload } from '@/components/specific/quickFilter/quickFilterTypes';
import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { FormInstance } from 'antd';

export enum GridNames {
  'customerPortalAddressGrid' = 'customerPortalAddressGrid',
  'customerPortalInvoiceGrid' = 'customerPortalInvoiceGrid',
  'customerPortalOrderGrid' = 'customerPortalOrderGrid',
  'customerPortalContactGrid' = 'customerPortalContactGrid',
}

export interface AppEvents {
  'columnManager:changed': {
    gridName: GridNames;
    gridState: any;
  };
  'columnManager:ready': {
    gridName: GridNames;
  };
  'idExistsOrNot:boolean': { status: boolean };
  CustomerPortalOrderQuickFilter: IQuickFilterEvent;
  CustomerPortalOrderQuickFilterTitleEvent: IQuickFilterTitleEvent;
}

export interface IQuickFilterEvent {
  form: FormInstance;
  onFilterApply: (values: { filters: IAssignedFilters[] }) => void;
  quickFilter: CreateSettingsDto<IQuickFilterPayload>;
  refetchFilters: (options?: RefetchOptions) => Promise<QueryObserverResult<any, Error>>;
  handleClearAll: () => void;
}

export interface IQuickFilterTitleEvent {
  title: string | undefined;
}
