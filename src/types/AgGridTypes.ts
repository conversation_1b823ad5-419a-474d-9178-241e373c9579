import {
  CellContextMenuEvent,
  Col<PERSON>ef,
  <PERSON>umn,
  Grid<PERSON><PERSON>s,
  ICellRenderer,
  SortChangedEvent,
} from 'ag-grid-community';
import { IContextMenuItems } from './ContextMenuTypes';
import { RefObject } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { GridNames } from './AppEvents';
import { IEmptyStatePageProps } from '@/components/common/emptyState/emptyStateTypes';
import { SettingsKeys } from '@/constant/SettingsKeys';

export interface IColDef extends ColDef {
  visible: boolean;
  field?: string;
}

export interface IPagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  onPaginationChange?: (page: number, limit: number) => void;
}
export type GridStorageKey = `${GridNames}` | `${SettingsKeys}`;

export interface IGridProps extends GridOptions {
  ref?: RefObject<AgGridReact>;
  gridRef?: RefObject<AgGridReact>;
  className?: string;
  gridId?: string;
  columnDefs: IColDef[];
  emptyState?: IEmptyStatePageProps;
  paginationProps?: IPagination;
  nullStateClassName?: string;
  gridName?: GridStorageKey;
}

export interface IWithContextMenu {
  isContextMenu: true;
  contextMenuItem: IContextMenuItems[];
  onContextMenu?: (params: CellContextMenuEvent) => void;
}

export interface IWithoutContextMenu {
  isContextMenu?: false;
}
export interface ICellRendererParams<T = unknown> extends ICellRenderer {
  data: T;
}

export type TGridProps = IGridProps & (IWithContextMenu | IWithoutContextMenu);

export interface IExtendedColumn extends Column {
  colId: string;
  sort: string;
}
export interface IExtendedSortChangedEvent extends SortChangedEvent {
  columns: IExtendedColumn[];
}
