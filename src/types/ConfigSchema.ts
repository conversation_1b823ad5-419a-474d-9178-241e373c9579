import { IAxiosDefaultConfig } from './AxiosTypes.ts';
import { IContextMenuConfig } from './ContextMenuTypes.ts';
import { INotificationConfig } from './NotificationTypes.ts';

export interface ConfigSchema {
  appName: string;
  apiUrl: string;
  baseUrl: string;
  showDetailedErrors: boolean;
  notification: INotificationConfig;
  AxiosDefaultConfig: IAxiosDefaultConfig;
  ContextMenuConfig: IContextMenuConfig;
  dateFormate: string;
  dateFormateWithoutTime: string;
  timeFormate12: string;
  timeFormate24: string;
  minPasswordLength: number;
  maxPasswordLength: number;
  maxPriceSetScheduleLimit: 15;
  otpLength: number;
  is12HoursFormate: boolean;
  allowEditOrderTill : number;
  units: {
    distance: string;
    weight: string;
  };
  mapCenter: {
    lat: number;
    lng: number;
  };
}
