import { InvoiceTemplateShadder, Lumigo<PERSON>ogo } from '@/assets';
import BuildingPrimaryIcon from '@/assets/icons/buildingPrimaryIcon';
import { PrimaryCheckedIcon } from '@/assets/icons/primaryCheckedIcon';
import UserPrimaryIcon from '@/assets/icons/userPrimaryIcon';
import Icon from '@ant-design/icons';
import './invoiceTemplate.css';

const InvoiceTemplateView = () => {
  return (
    <div className="bg-white min-h-screen p-6 text-primary-900 w-full">
      <div
        className="max-w-4xl mx-auto rounded-lg overflow-hidden px-10 py-8 relative "
        style={{ boxShadow: '0px 10px 20px 0px rgba(37, 49, 76, 0.15)' }}
      >
        <img
          src={InvoiceTemplateShadder}
          alt=""
          className="absolute left-0 top-0 mix-blend-multiply z-10"
        />
        <div className="flex justify-between items-center mb-6 gap-4 relative z-50">
          <div>
            <img src={LumigoLogo} alt="Lumigo Logo" className="h-8 mb-2" />
            <p className="text-sm leading-5">
              Lumigo Solution 2025 Inc (Lumigo Transport) <br />
              Phone: ************ Website: www.lumigotransport.ca
            </p>
          </div>
          <div className="text-left pl-5 border-l border-l-gary-300">
            <div className="text-sm text-primary-200">Invoice number:</div>
            <div className="font-semibold text-sm mb-2">INV000027</div>
            <div className="text-sm text-primary-200">Issued:</div>
            <div className="font-semibold text-sm">June 26, 2024</div>
          </div>
        </div>
        <div className="flex gap-2.5 bg-white text-sm border border-primary-50 p-6 rounded-lg relative z-50">
          <div>
            <h3 className="font-semibold mb-2 text-xs flex gap-2 items-center">
              <Icon component={PrimaryCheckedIcon} />
              BILL TO:
            </h3>
            <div className="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]">
              <p className="font-medium text-gray-800 mb-4 text-sm">
                <Icon component={UserPrimaryIcon} className="mr-" /> Mauro Sicard
              </p>
              <p className="mb-2">(612) 865 - 0989</p>
              <p className="mb-2"><EMAIL></p>
              <p>Palo Alto, San Francisco, CA 92102, United States of America</p>
            </div>
          </div>
          <div>
            <h3 className="font-semibold mb-2 text-xs flex gap-2 items-center">
              <Icon component={PrimaryCheckedIcon} /> BILL FROM:
            </h3>
            <div className="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]">
              <p className="font-medium text-gray-800 mb-4 text-sm">
                <Icon component={BuildingPrimaryIcon} className="mr-1.5" />
                BRIX Agency
              </p>
              <p className="mb-2">(684) 879 - 0102</p>
              <p className="mb-2">Palo Alto, San Francisco, CA 94110, United States of America</p>
              <p className="mb-2"></p>
              <p>12345 6789 US0001</p>
            </div>
          </div>
          <div className="h-full">
            <h3 className="font-semibold mb-2 text-xs flex gap-2 items-center">
              <Icon component={PrimaryCheckedIcon} /> AMOUNT DUE:
            </h3>
            <div className=" border border-primary-50 rounded-lg p-6 px-7 bg-primary-500 text-primary-300 h-[calc(100%-22px)]">
              <p className="text-sm text-white mb-1">CAD</p>
              <p className="text-2xl font-bold text-white mb-2">$19,570.00</p>
              <p className="text-sm text-white mb-2">July 26, 2024</p>
              <span className="block w-fit text-green-600 font-semibold success-chip !px-2 !py-1">
                Paid
              </span>
            </div>
          </div>
        </div>

        <div className="mt-8 overflow-auto border border-primary-50 rounded-lg">
          <table className="template-table min-w-full text-sm text-left !text-primary-900">
            <thead>
              <tr className="bg-primary-25">
                <th>Tracking Number</th>
                <th>Service Level</th>
                <th>Completed Date</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {[
                ['9092984', 'Express 2hr service', '16/06/2020 05:30 PM', '$70,000'],
                ['5637657', '4hr service', '30/01/2020 04:00 PM', '$40,000'],
                ['8656436', '2 hr service', '19/07/2020 10:00 AM', '$50,000'],
                ['9002984', '4 hr service', '31/03/2020 11:30 AM', '$80,000'],
                ['5236852', 'Next day service', '27/02/2020 06:00 PM', '$60,000'],
                ['3342765', 'Express 2hr service', '09/04/2020 03:30 AM', '$70,000'],
                ['5236850', '2 hr service', '12/06/2020 04:30 PM', '$100,000'],
                ['7523765', '2 hr service', '26/04/2020 11:30 AM', '$40,000'],
                ['1283746', 'Same day service', '15/05/2020 01:00 PM', '$55,000'],
                ['6482736', 'Standard delivery', '20/05/2020 02:30 PM', '$30,000'],
                ['2938475', 'Overnight service', '10/06/2020 08:00 PM', '$90,000'],
                ['9837462', 'Express 1hr service', '18/06/2020 06:15 PM', '$110,000'],
                ['3847562', 'Weekend delivery', '22/06/2020 07:45 AM', '$65,000'],
                ['8473629', '4hr service', '25/06/2020 09:00 AM', '$45,000'],
                ['1938476', 'Next day service', '28/06/2020 10:30 AM', '$50,000'],
                ['8472635', '2 hr service', '30/06/2020 03:00 PM', '$35,000'],
              ].map(([tn, sl, cd, amnt], idx) => (
                <tr key={idx} className="border-t">
                  <td>{tn}</td>
                  <td>{sl}</td>
                  <td>{cd}</td>
                  <td>{amnt}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="grid grid-cols-2 gap-3 mt-8 text-sm relative z-50">
          <div className="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24">
            <div className="flex justify-between items-center">
              <p className="font-medium">Total Orders</p>
              <p className="font-bold">16</p>
            </div>
            <div className="flex justify-between mt-1 items-center">
              <p className="font-medium mt-2">Balance</p>
              <p className="font-bold">$0.00</p>
            </div>
          </div>
          <div className="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24">
            <p className="flex justify-between items-center">
              Sub total <span className="font-bold">$100.00</span>
            </p>
            <p className="flex justify-between items-center mt-2">
              GST <span className="font-bold">$3.00</span>
            </p>
            <p className="flex justify-between items-center mt-2">
              QST <span className="font-bold">$9.00</span>
            </p>
            <p className="flex justify-between items-center mt-2">
              Total amount <span className="font-bold">$112.00</span>
            </p>
          </div>
        </div>

        <div className="mt-6 text-xs text-primary-900 leading-[18px]">
          <p>
            <span className="font-bold text-sm">Terms & Conditions:</span>
          </p>
          <p>
            Fees and payment terms will be established in the contract or agreement prior to the
            commencement of the project. An initial deposit will be required before any design work
            begins. We reserve the right to suspend or halt work in the event of non-payment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default InvoiceTemplateView;
