import React, { useState } from 'react';
import { Button, Form, Input, Typography } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { AuthLayout } from '@/components/layout/AuthLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import './loginPage.css';
import { noWhitespaceValidator } from '@/lib/FormValidators';
import { useConfig } from '@/contexts/ConfigContext';
import { LoginFormValues } from './authTypes';
import { login } from '@/api/auth/auth.service';
import useThrottle from '@/hooks/useThrottle';

const { Text } = Typography;

const LoginPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [form] = Form.useForm<LoginFormValues>();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const { config } = useConfig();

  const handleSubmit = useThrottle(async (values: LoginFormValues) => {
    setIsLoading(true);
    setHasError(false);
    try {
      const response = await login(values);

      if (response) {
        navigate(ROUTES.ORDER.ORDER_ENTRY);
      }
    } finally {
      setIsLoading(false);
    }
  }, 3000);

  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto">
        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          className="auth-form"
          disabled={isLoading}
          onValuesChange={() => hasError && setHasError(false)}
        >
          <Form.Item
            name="email"
            label={t('auth.form.email.label')}
            validateFirst
            rules={[
              { required: true, message: t('auth.emailRequired') },
              { type: 'email', message: t('auth.invalidEmail') },
            ]}
          >
            <Input
              prefix={<UserOutlined className="text-gray-400" />}
              placeholder={t('auth.form.email.placeholder')}
              autoComplete="email"
              autoFocus
              status={hasError ? 'error' : ''}
              maxLength={255}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label={t('auth.form.password.label')}
            validateFirst
            rules={[
              {
                validator: (_, value) =>
                  noWhitespaceValidator(_, value, t('auth.form.password.notContainWhiteSpace')),
              },
              { whitespace: true, message: t('auth.form.password.invalidPassword') },
              { required: true, message: t('auth.passwordRequired') },
              {
                min: config.minPasswordLength,
                message: t('auth.passwordTooShort', { length: String(config.minPasswordLength) }),
              },
            ]}
            help={hasError ? t('auth.invalidCredentials') : undefined}
            validateStatus={hasError ? 'error' : undefined}
          >
            <Input.Password
              prefix={<LockOutlined className="text-gray-400" />}
              placeholder={t('auth.form.password.placeholder')}
              autoComplete="current-password"
              maxLength={config.maxPasswordLength}
              status={hasError ? 'error' : ''}
            />
          </Form.Item>
          <Link to={ROUTES.COMMON.FORGET_PASSWORD}>
            <div className="flex justify-end mb-4">
              <Typography.Link className="text-[1rem] !text-primary-600 font-medium !font-Inter">
                {t('auth.forgotPassword')}
              </Typography.Link>
            </div>
          </Link>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              className={`h-[48px] bg-primary-600 text-white !hover:bg-red-600 text-sm ${hasError ? 'bg-red-500 hover:bg-red-600 border-red-500' : ''}`}
            >
              {isLoading ? t('auth.authenticating') : t('auth.loginButton')}
            </Button>
          </Form.Item>
          <div className="text-center">
            <Text type="secondary" className="!text-[#20363F] !font-Inter">
              {t('auth.noAccount')}{' '}
              <Typography.Link href="/register" className="!text-primary-600 !font-Inter">
                {t('auth.signUpLink')}
              </Typography.Link>
            </Text>
          </div>
        </Form>
      </div>
    </AuthLayout>
  );
};

export default LoginPage;
