import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import CheckoutForm from './CheckoutForm';
import { Card } from 'antd';
import { Dispatch, SetStateAction } from 'react';

// Load Stripe outside of component render
const stripePromise = loadStripe('pk_test_TYooMQauvdEDq54NiTphI7jx');

interface IPaymentPageProps {
  setIsAllFilledUp: Dispatch<SetStateAction<boolean>>;
  isStripeRequired: boolean;
}
const PaymentsPage: React.FC<IPaymentPageProps> = (props) => {
  const { setIsAllFilledUp, isStripeRequired } = props;
  return (
    <div className="payment-summary-container">
      <Card
        title={
          <div className="payment-summary-header">
            <span>Card details</span>
          </div>
        }
        className="payment-summary-card"
        bordered={true}
      >
        <Elements
          stripe={stripePromise}
          options={{
            appearance: {
              theme: 'stripe',
              variables: {
                colorPrimary: '#0570de',
                colorBackground: '#ffffff',
                colorText: '#30313d',
              },
            },
          }}
        >
          <CheckoutForm setIsAllFilledUp={setIsAllFilledUp} isStripeRequired={isStripeRequired} />
        </Elements>
      </Card>
    </div>
  );
};

export default PaymentsPage;
