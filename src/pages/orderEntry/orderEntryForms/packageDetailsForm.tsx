import { Form, Select, InputNumber, Row, Col, Button, Input, message, Divider } from 'antd';
import { useState, useEffect } from 'react';
import './addressForms.css';
import { EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { DeleteIcon } from '@/assets';
import { useLanguage } from '@/hooks/useLanguage';
import { numberFieldValidator } from '@/lib/FormValidators';
import { FormInstance, UploadFile } from 'antd/lib';
import { IPackage } from '@/api/packages/packages.types';
import { UploadChangeParam } from 'antd/es/upload';
import { IPackages } from '@/api/order/order.types';

const MAX_TOTAL_SIZE_MB = 20;
const MAX_TOTAL_SIZE_BYTES = MAX_TOTAL_SIZE_MB * 1024 * 1024;
interface IPackageFormDetailsForm {
  setPackageFormDetails: (details: IPackages[]) => void;
  packageList: IPackage[];
  form: FormInstance;
  orderDetails: any;
  isEditMode?: boolean; // Add this prop to explicitly control edit vs create mode
  isAbleToEdit: boolean
}
const PackageDetailsForm: React.FC<IPackageFormDetailsForm> = (props) => {
  const { setPackageFormDetails, packageList, form, orderDetails, isAbleToEdit } = props;
  const { t } = useLanguage();
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [filePreviewMap, setFilePreviewMap] = useState<Record<number, UploadFile[]>>({});
  const [isInitialForm, setIsInitialForm] = useState<boolean>(true);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [isSelectedPackagePrimary, setIsSelectedPackagePrimary] = useState(false);
  const [showNewForm, setShowNewForm] = useState(true);
  const [validationForPackage, setValidationForPackage] = useState<{
    dimensionsRequired: boolean;
    weightRequired: boolean;
    maxWeight?: number;
    maxVolume?: number;
    length?: number;
    height?: number;
    width?: number;
  }>({
    dimensionsRequired: true,
    weightRequired: true,
    maxWeight: 0,
    maxVolume: 0,
    length: 0,
    height: 0,
    width: 0,
  });
  const [maxValuesForPackage, setMaxValuesForPackage] = useState<
    Record<number, { length?: number; width?: number; height?: number; maxWeight?: number }>
  >({});
  useEffect(() => {
    const formPackages = form.getFieldValue('packages');
    if (Array.isArray(formPackages) && formPackages.length > 0) {
      const newMaxValues: Record<
        number,
        { length?: number; width?: number; height?: number; maxWeight?: number }
      > = {};
      formPackages.forEach((pkg: any, idx: number) => {
        const selected = packageList.find(
          (p) => p.id === pkg?.packageTemplateId || p.name === pkg?.packageType
        );
        if (selected) {
          newMaxValues[idx] = {
            length: selected.length,
            width: selected.width,
            height: selected.height,
            maxWeight: selected.maxWeight,
          };
        }
      });
      setMaxValuesForPackage(newMaxValues);
    }
  }, [form, packageList]);
  useEffect(() => {
    const formValues = form.getFieldsValue();
    if (
      !formValues.packages ||
      !Array.isArray(formValues.packages) ||
      formValues.packages.length === 0
    ) {
      setPackageFormDetails([]);
      return;
    }

    const formattedPackages = formValues.packages
      .filter((pkg: IPackages) => pkg && typeof pkg === 'object')
      .map((pkg: IPackages) => ({
        ...pkg,
        height: pkg?.height && Number(pkg.height),
        width: pkg?.width && Number(pkg?.width),
        length: pkg?.length && Number(pkg?.length),
        totalWeight: pkg?.totalWeight && Number(pkg?.totalWeight),
        quantity: pkg?.quantity && Number(pkg?.quantity),
        declaredValue: pkg?.declaredValue && Number(pkg?.declaredValue),
        packageType: pkg?.packageTemplateName,
      }));

    if (
      formattedPackages.length > 0 &&
      formattedPackages.some(
        (pkg: IPackage) =>
          pkg &&
          Object.values(pkg).some((value) => value !== undefined && value !== null && value !== '')
      )
    ) {
      setPackageFormDetails(formattedPackages);
    } else {
      setPackageFormDetails([]);
    }
  }, [form.getFieldValue('packages')]);
  useEffect(() => {
    if (orderDetails?.items?.length > 0) {
      const newFilePreviewMap: Record<number, UploadFile[]> = {};
      orderDetails.items.forEach((item: IPackages, index: number) => {
        const height = item?.height && Number(item.height);
        const width = item?.width && Number(item?.width);
        const length = item?.length && Number(item?.length);
        const cubicDimension = `${length} × ${width} × ${height}`;
        form.setFieldValue(['packages', index, 'cubicDimension'], cubicDimension);
        if (item?.packageTemplateName) {
          form.setFieldValue(['packages', index, 'packageType'], item.packageTemplateName);
        }

        if (item?.imageUrl) {
          const parsedData = JSON.parse(item.imageUrl);
          if (parsedData?.fileList) {
            newFilePreviewMap[index] = parsedData.fileList;
          }
        }
      });
      setFilePreviewMap(newFilePreviewMap);
    }
  }, [orderDetails]);
  useEffect(() => {
    const packages = form.getFieldValue('packages') || [];
    setFilePreviewMap((prev) => {
      const rebuiltMap: Record<number, UploadFile[]> = {};
      packages.forEach((_pkg: any, idx: number) => {
        rebuiltMap[idx] = prev[idx] || [];
      });
      return rebuiltMap;
    });
  }, [form.getFieldValue('packages')]);

  const validateFiles = (_: any, value: any) => {
    if (!value || !value.fileList || value.fileList.length === 0) {
      return Promise.resolve();
    }
    if (value.fileList.length > 1) {
      return Promise.reject(new Error(t('orderEntryForms.address.onlySingleFileAllowed')));
    }
    const totalSize = value.fileList.reduce((sum: number, file: UploadFile) => {
      return sum + (file.originFileObj?.size || 0);
    }, 0);

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      return Promise.reject(
        new Error(
          t('orderEntryForms.packageDetails.errors.fileSizeExceeded', {
            size: (totalSize / (1024 * 1024)).toFixed(2),
            limit: MAX_TOTAL_SIZE_MB,
          })
        )
      );
    }

    return Promise.resolve();
  };

  const toggleEdit = (index: number) => {
    if (editingIndex === index) {
      setEditingIndex(null);
    } else {
      setEditingIndex(index);
    }
  };
  const updateCubicDimension = (index: number) => {
    const length = form.getFieldValue(['packages', index, 'length']);
    const width = form.getFieldValue(['packages', index, 'width']);
    const height = form.getFieldValue(['packages', index, 'height']);

    const cubicDimension = `${length || 'Length'} × ${width || 'Width'} × ${height || 'Height'}`;
    form.setFieldValue(['packages', index, 'cubicDimension'], cubicDimension);
  };

  const handleFileChange = (info: UploadChangeParam, fieldPath: string) => {
    let { fileList } = info;

    fileList = fileList.map((file: UploadFile, idx: number) => ({
      ...file,
      uid: file.uid || `${Date.now()}-${idx}`,
      status: file.status || 'done',
      url: file.url || (file.originFileObj && URL.createObjectURL(file.originFileObj)),
    }));

    form.setFieldValue(fieldPath, {
      file: fileList[fileList.length - 1],
      fileList,
    });

    const packageIndex = fieldPath[1];
    setFilePreviewMap((prev) => ({
      ...prev,
      [packageIndex]: fileList,
    }));

    const totalSize = fileList.reduce((sum: number, file: UploadFile) => {
      return sum + (file.originFileObj?.size || 0);
    }, 0);

    if (totalSize > MAX_TOTAL_SIZE_BYTES) {
      message.warning(
        t('orderEntryForms.packageDetails.warnings.fileSizeExceeded', {
          size: (totalSize / (1024 * 1024))?.toFixed(2),
          limit: MAX_TOTAL_SIZE_MB,
        })
      );
    }

    form.validateFields([fieldPath]);
  };

  const handleFileDelete = (packageIndex: number, fileIndex: number) => {
    setFilePreviewMap((prev) => {
      const currentFiles = prev[packageIndex] || [];
      if (currentFiles.length === 0) return prev;
      const newFileList = [...currentFiles];
      newFileList.splice(fileIndex, 1);
      const fieldPath = ['packages', packageIndex, 'imageUrl'] as unknown as string;
      form.setFieldValue(fieldPath, {
        file: newFileList.length > 0 ? newFileList[newFileList.length - 1] : null,
        fileList: newFileList,
      });
      form.validateFields([fieldPath]);
      return {
        ...prev,
        [packageIndex]: newFileList,
      };
    });
  };

  const handlePackageTypeChange = (value: string, index: number) => {
    setEditingIndex(index);
    if (value) {
      const selectedPackage = packageList.find((item: IPackage) => item.id === value);
      if (selectedPackage) {
        const isPrimaryPackage = selectedPackage.metadata?.isPrimary === true;
        setIsSelectedPackagePrimary(isPrimaryPackage);
        setMaxValuesForPackage((prev) => ({
          ...prev,
          [index]: {
            length: selectedPackage.length,
            width: selectedPackage.width,
            height: selectedPackage.height,
            maxWeight: selectedPackage.maxWeight,
          },
        }));
        if (isPrimaryPackage) {
          setValidationForPackage({
            dimensionsRequired: true,
            weightRequired: true,
            maxWeight: 0,
            maxVolume: 0,
            length: 0,
            height: 0,
            width: 0,
          });
          const currentValues = form.getFieldValue(['packages', index]);
          form.setFieldsValue({
            packages: {
              [index]: {
                ...currentValues,
                packageType: selectedPackage?.name,
                packageTemplateId: selectedPackage?.id,
                packageTemplateName: selectedPackage?.name,
              },
            },
          });

          form.setFields([
            {
              name: ['packages', index, 'length'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'width'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'height'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'totalWeight'],
              errors: [],
              validating: false,
            },
          ]);
        } else {
          setValidationForPackage({
            dimensionsRequired: selectedPackage.dimensionsRequired,
            weightRequired: selectedPackage.weightRequired,
            length: selectedPackage.length,
            height: selectedPackage.height,
            width: selectedPackage.width,
            maxWeight: selectedPackage.maxWeight,
          });

          form.setFieldsValue({
            packages: {
              [index]: {
                packageType: selectedPackage?.name,
                packageTemplateId: selectedPackage?.id,
                packageTemplateName: selectedPackage?.name,
              },
            },
          });

          form.setFields([
            {
              name: ['packages', index, 'length'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'width'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'height'],
              errors: [],
              validating: false,
            },
            {
              name: ['packages', index, 'totalWeight'],
              errors: [],
              validating: false,
            },
          ]);
        }
      }
    }
  };

  const packageLength = form.getFieldValue('packages')?.length;
  useEffect(() => {
    setShouldScroll(packageLength > 5);
  }, [packageLength]);

  const packageHasValue = (index: number) => {
    const packages = form.getFieldValue('packages') || [];
    const packageAtGivenIndex = packages[index];
    if (
      !packageAtGivenIndex ||
      typeof packageAtGivenIndex !== 'object' ||
      !Object.values(packageAtGivenIndex).some(
        (value) => value !== undefined && value !== null && value !== ''
      )
    ) {
      return false;
    }

    return true;
  };

  useEffect(() => {
    const packages = form.getFieldValue('packages') || [];
    if (packages.length === 1) {
      setShowNewForm(true);
      setIsInitialForm(false);
    } else if (packages.length === 0) {
      setShowNewForm(true);
      setEditingIndex(null);
      setIsInitialForm(true);
    } else if (packages.length > 1) {
      setShowNewForm(true);
      setIsInitialForm(false);
    }
  }, [form.getFieldValue('packages')]);
  return (
    <Form
      className="package-details-form"
      form={form}
      initialValues={{ packages: [{}] }}
      layout="vertical"
    >
      {
        <div
          className={shouldScroll ? 'max-h-[500px] overflow-y-auto pr-2' : ''}
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#d1d5db transparent',
          }}
        >
          <Form.List name="packages">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => {
                  const packages = form.getFieldValue('packages') || [];
                  const actualPackages = packages.filter(
                    (pkg: any) =>
                      pkg &&
                      typeof pkg === 'object' &&
                      Object.values(pkg).some(
                        (value) => value !== undefined && value !== null && value !== ''
                      )
                  );
                  let isEditing: boolean = false;
                  const isLast = index === actualPackages.length;

                  if (isLast && !showNewForm) {
                    return null;
                  }

                  isEditing =
                    editingIndex === index || (editingIndex === null && isLast && showNewForm);
                  const fieldPath: string = ['packages', index, 'imageUrl'] as unknown as string;

                  return (
                    <div key={field.key} className="mb-6 bg-white rounded-lg p-[24px] pb-[0px]">
                      {isLast &&
                        fields.length > 1 &&
                        showNewForm &&
                        (() => {
                          const hasMultiplePackages = actualPackages.length > 1;
                          const hasFormVisible = showNewForm && actualPackages.length >= 1;
                          const shouldShowRemove =
                            (hasMultiplePackages || hasFormVisible) && isEditing;
                          if (!showNewForm && actualPackages.length === 1) {
                            return false;
                          }

                          return shouldShowRemove;
                        })() && (
                          <div className="flex justify-end mb-2">
                            <Button
                              danger
                              icon={<DeleteIcon />}
                              type="text"
                              onClick={() => {
                                if (fields.length > 1) {
                                  remove(index);
                                  setShowNewForm(false);
                                  setEditingIndex(null);

                                  setFilePreviewMap((prev) => {
                                    const newMap = { ...prev };
                                    delete newMap[index];
                                    return newMap;
                                  });
                                }
                              }}
                              className="text-error-600 p-2 border border-error-500 rounded-md"
                            >
                              {t('common.buttons.remove')}
                            </Button>
                          </div>
                        )}

                      <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center">
                          {(index !== fields.length ||
                            editingIndex === null ||
                            editingIndex === index) && (
                            <span className="border border-primary-600 rounded-md text-primary-600 text-sm font-medium px-3 py-1">
                              {t('orderEntryForms.packageDetails.package')} {index + 1}
                            </span>
                          )}
                        </div>
                        {!isEditing && !isInitialForm && index !== actualPackages.length && (
                          <div className="flex items-center gap-3">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              onClick={() => toggleEdit(index)}
                              className="mr-2 text-[#333] border border-primary-100 rounded-md"
                            />
                            {(() => {
                              const packages = form.getFieldValue('packages') || [];
                              const actualPackages = packages.filter(
                                (pkg: any) =>
                                  pkg &&
                                  typeof pkg === 'object' &&
                                  Object.values(pkg).some(
                                    (value) => value !== undefined && value !== null && value !== ''
                                  )
                              );

                              const shouldShowRemove =
                                actualPackages.length > 1 ||
                                (actualPackages.length === 1 &&
                                  index === actualPackages.length + 1);
                              return shouldShowRemove ? (
                                <Button
                                  disabled={fields.length === 0}
                                  danger
                                  icon={<DeleteIcon />}
                                  type="text"
                                  onClick={() => {
                                    if (fields.length > 1) {
                                      remove(index);
                                      setEditingIndex(null);
                                      setShowNewForm(false);

                                      // const newLastIndex = fields.length - 2;
                                      // setEditingIndex(newLastIndex >= 0 ? newLastIndex : 0);

                                      setFilePreviewMap((prev) => {
                                        const newMap = { ...prev };
                                        delete newMap[index];
                                        const updatedMap: Record<number, UploadFile[]> = {};
                                        Object.entries(newMap).forEach(([key, value]) => {
                                          const numKey = parseInt(key, 10);
                                          updatedMap[numKey > index ? numKey - 1 : numKey] = value;
                                        });
                                        return updatedMap;
                                      });

                                      setMaxValuesForPackage((prev) => {
                                        const newMap = { ...prev };
                                        delete newMap[index];
                                        const updatedMap: Record<
                                          number,
                                          {
                                            length?: number;
                                            width?: number;
                                            height?: number;
                                            maxWeight?: number;
                                          }
                                        > = {};
                                        Object.entries(newMap).forEach(([key, value]) => {
                                          const numKey = parseInt(key, 10);
                                          updatedMap[numKey > index ? numKey - 1 : numKey] = value;
                                        });
                                        return updatedMap;
                                      });
                                    }
                                  }}
                                  className="text-error-600 p-2 border border-error-500 rounded-md"
                                >
                                  {t('common.buttons.remove')}
                                </Button>
                              ) : null;
                            })()}
                          </div>
                        )}
                        {isEditing &&
                          editingIndex === index &&
                          index !== actualPackages.length &&
                          actualPackages.length > 1 && (
                            <div className="flex items-center gap-3">
                              <Button
                                type="text"
                                onClick={() => toggleEdit(index)}
                                className="text-[#333] border border-primary-100 rounded-md"
                              >
                                {t('common.buttons.cancel')}
                              </Button>
                              <Button
                                type="text"
                                onClick={() => toggleEdit(index)}
                                className="text-[#333] bg-primary-600 text-white border border-primary-600 rounded-md"
                              >
                                {t('common.buttons.update')}
                              </Button>
                            </div>
                          )}
                      </div>

                      {isEditing ? (
                        <>
                          <Row gutter={16} className="mb-4">
                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'packageType']}
                                fieldKey={[field.fieldKey as number, 'packageType']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.packagingType')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.packageTypeRequired'
                                    ),
                                  },
                                ]}
                              >
                                <Select
                                  placeholder={t(
                                    'orderEntryForms.packageDetails.selectPackageType'
                                  )}
                                  className="address-select-item"
                                  options={packageList}
                                  onChange={(value) => handlePackageTypeChange(value, index)}
                                />
                              </Form.Item>
                            </Col>
                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'quantity']}
                                fieldKey={[field.fieldKey as number, 'quantity']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.quantity')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.quantityRequired'
                                    ),
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (value > 99999) {
                                        return Promise.reject(
                                          new Error(t('priceModifiers.maximumValueExceeded'))
                                        );
                                      } else if (value < 0) {
                                        return Promise.reject(
                                          new Error(
                                            t(
                                              'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                            )
                                          )
                                        );
                                      } else {
                                        return Promise.resolve();
                                      }
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  maxLength={5}
                                  className="address-select-item"
                                  min={1}
                                  placeholder="1"
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          <Row gutter={16} className="mb-4">
                            <Col xs={24} md={12}>
                              <Form.Item
                                rules={[
                                  {
                                    required: validationForPackage?.dimensionsRequired,
                                    message: 'Dimensions are required',
                                  },
                                ]}
                                dependencies={['length', 'width', 'height']}
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'cubicDimension']}
                                fieldKey={[field.fieldKey as number, 'cubicDimension']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.cubicDimension')}
                                  </span>
                                }
                              >
                                <Input
                                  className="address-select-item"
                                  placeholder={t(
                                    'orderEntryForms.packageDetails.dimensionsPlaceholder'
                                  )}
                                  disabled
                                />
                              </Form.Item>
                            </Col>

                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'totalWeight']}
                                fieldKey={[field.fieldKey as number, 'totalWeight']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.combinedWeight')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.weightRequired'
                                    ),
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (isSelectedPackagePrimary) {
                                        return Promise.resolve();
                                      }
                                      const max = maxValuesForPackage[index]?.maxWeight;
                                      if (max && value > max) {
                                        return Promise.reject(
                                          new Error(t('orderEntryForms.address.maxWeightExceeded'))
                                        );
                                      } else if (value < 0) {
                                        return Promise.reject(
                                          new Error(
                                            t(
                                              'priceModifiers.configureTiersForm.pleaseEnterValidNumber'
                                            )
                                          )
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  className="address-select-item"
                                  min={1}
                                  step={0.1}
                                  placeholder="0.0"
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          <Row gutter={16} className="mb-4">
                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'length']}
                                fieldKey={[field.fieldKey as number, 'length']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.length')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.lengthRequired'
                                    ),
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (isSelectedPackagePrimary) {
                                        return Promise.resolve();
                                      }
                                      const max = maxValuesForPackage[index]?.length;
                                      if (max && value > max) {
                                        return Promise.reject(
                                          new Error(
                                            t('orderEntryForms.address.maxValueAllowed', {
                                              maxValue: max,
                                              field: 'length',
                                            })
                                          )
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  step={0.01}
                                  maxLength={5}
                                  className="address-select-item"
                                  min={1}
                                  placeholder="0"
                                  onChange={() => {
                                    updateCubicDimension(index);
                                  }}
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>

                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'width']}
                                fieldKey={[field.fieldKey as number, 'width']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.width')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.widthRequired'
                                    ),
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (isSelectedPackagePrimary) {
                                        return Promise.resolve();
                                      }
                                      const max = maxValuesForPackage[index]?.width;
                                      if (max && value > max) {
                                        return Promise.reject(
                                          new Error(
                                            t('orderEntryForms.address.maxValueAllowed', {
                                              maxValue: max,
                                              field: 'width',
                                            })
                                          )
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  step={0.1}
                                  maxLength={6}
                                  className="address-select-item"
                                  min={1}
                                  placeholder="0"
                                  onChange={() => {
                                    updateCubicDimension(index);
                                  }}
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          <Row gutter={16} className="mb-4">
                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'height']}
                                fieldKey={[field.fieldKey as number, 'height']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.height')}
                                  </span>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      'orderEntryForms.packageDetails.errors.heightRequired'
                                    ),
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (isSelectedPackagePrimary) {
                                        return Promise.resolve();
                                      }
                                      const max = maxValuesForPackage[index]?.height;
                                      if (max && value > max) {
                                        return Promise.reject(
                                          new Error(
                                            t('orderEntryForms.address.maxValueAllowed', {
                                              maxValue: max,
                                              field: 'height',
                                            })
                                          )
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  step={0.1}
                                  maxLength={6}
                                  className="address-select-item"
                                  min={1}
                                  placeholder="0"
                                  onChange={() => {
                                    updateCubicDimension(index);
                                  }}
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>

                            <Col xs={24} md={12}>
                              <Form.Item
                                className="address-form-item"
                                {...field}
                                name={[field.name, 'declaredValue']}
                                fieldKey={[field.fieldKey as number, 'declaredValue']}
                                label={
                                  <span className="text-sm font-medium">
                                    {t('orderEntryForms.packageDetails.declaredValue')}
                                  </span>
                                }
                              >
                                <InputNumber
                                  maxLength={6}
                                  className="address-select-item"
                                  min={1}
                                  addonBefore="$"
                                  placeholder="0.00"
                                  precision={2}
                                  onKeyDown={(e) =>
                                    numberFieldValidator(e, { allowDecimals: true })
                                  }
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          <Row gutter={16}>
                            <Col span={24}>
                              <Form.Item
                                dependencies={[orderDetails]}
                                className="address-form-item !mb-0"
                                {...field}
                                name={[field.name, 'imageUrl']}
                                fieldKey={[field.fieldKey as number, 'imageUrl']}
                                label={
                                  <div className="flex items-center">
                                    <span className="text-sm font-medium mr-1">
                                      {t('orderEntryForms.packageDetails.uploadImage')}
                                    </span>
                                    <InfoCircleOutlined className="text-gray-400" />
                                  </div>
                                }
                                rules={[
                                  {
                                    validator: validateFiles,
                                  },
                                ]}
                                validateTrigger={['onChange', 'onBlur']}
                              >
                                <CustomUpload
                                  label={' '}
                                  form={form}
                                  name={fieldPath}
                                  placeholder={t(
                                    'orderEntryForms.packageDetails.uploadImagePlaceholder'
                                  )}
                                  uploadButtonText={t('common.buttons.upload')}
                                  uploadComponentProps={{
                                    maxCount: 1,
                                    multiple: false,
                                    listType: 'picture',
                                    beforeUpload: () => false,
                                    onChange: (info) => handleFileChange(info, fieldPath),
                                    showUploadList: false,
                                  }}
                                  fileList={filePreviewMap[index] || []}
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          {filePreviewMap[index] && filePreviewMap[index].length > 0 && (
                            <div className="mt-2">
                              <div className="flex flex-wrap gap-2">
                                {filePreviewMap[index].map(
                                  (file: UploadFile, fileIndex: number) => {
                                    return (
                                      <div
                                        key={fileIndex}
                                        className="relative w-16 h-16 border rounded p-1"
                                      >
                                        <div
                                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center cursor-pointer"
                                          onClick={() => handleFileDelete(index, fileIndex)}
                                        >
                                          ×
                                        </div>
                                        <img
                                          src={
                                            file.originFileObj instanceof File
                                              ? URL.createObjectURL(file.originFileObj)
                                              : file.url
                                          }
                                          alt={`preview ${fileIndex}`}
                                          className="w-full h-full object-contain"
                                        />
                                      </div>
                                    );
                                  }
                                )}
                              </div>
                            </div>
                          )}
                        </>
                      ) : (
                        !isEditing &&
                        editingIndex !== index &&
                        index !== actualPackages.length && (
                          <div className="border-0 p-0">
                            <Row gutter={[16, 16]}>
                              <Col span={5}>
                                <div className="text-[16px] font-[600]">
                                  {t('orderEntryForms.packageDetails.packageType')}
                                </div>
                                <div className="text-[16px]">
                                  {form.getFieldValue(['packages', index, 'packageType']) || '-'}
                                </div>
                              </Col>
                              <Divider type="vertical" className="h-8 !bg-primmay-100" />
                              <Col span={5}>
                                <div className="text-[16px] font-[600]">
                                  {t('orderEntryForms.packageDetails.quantity')}
                                </div>
                                <div className="text-[16px]">
                                  {form.getFieldValue(['packages', index, 'quantity']) || '-'}
                                </div>
                              </Col>
                              <Divider type="vertical" className="h-8 !bg-primmay-100" />

                              <Col span={5}>
                                <div className="text-[16px] font-[600]">
                                  {t('orderEntryForms.packageDetails.cubicDimension')}
                                </div>
                                <div className="text-[16px]">
                                  {form.getFieldValue(['packages', index, 'cubicDimension']) || '-'}
                                </div>
                              </Col>
                              <Divider type="vertical" className="h-8 !bg-primmay-100" />

                              <Col span={5}>
                                <div className="text-[16px] font-[600]">
                                  {t('orderEntryForms.packageDetails.combinedWeight')}{' '}
                                  <span className="text-[14px] font-[400] text-gray-500">(Kg)</span>
                                </div>
                                <div className="text-[16px]">
                                  {form.getFieldValue(['packages', index, 'totalWeight']) || '-'}
                                </div>
                              </Col>
                            </Row>
                            {filePreviewMap[index] && filePreviewMap[index].length > 0 && (
                              <div className="mt-4">
                                <div className="text-[16px] font-[600] mb-2">
                                  {t('orderEntryForms.packageDetails.uploadedImages')}
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  {filePreviewMap[index].map(
                                    (file: UploadFile, fileIndex: number) => (
                                      <div key={fileIndex} className="w-16 h-16 border rounded p-1">
                                        <img
                                          src={
                                            file.originFileObj instanceof File
                                              ? URL.createObjectURL(file.originFileObj)
                                              : file.url
                                          }
                                          alt={`preview ${fileIndex}`}
                                          className="w-full h-full object-contain"
                                        />
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      )}
                      {index !== fields.length - 1 && <Divider className="!bg-primmay-100" />}
                    </div>
                  );
                })}

               <div className="w-full bg-primary-25 p-3 flex justify-end mt-4">
                  <Button
                  disabled={!isAbleToEdit}
                    type="primary"
                    onClick={() => {
                      setIsInitialForm(false);
                      if (!showNewForm) {
                        setShowNewForm(true);
                      } else {
                        const lastIndex = fields.length - 1;
                        const lastPackage = form.getFieldValue(['packages', lastIndex]);
                        if (
                          !lastPackage ||
                          (lastPackage &&
                            typeof lastPackage === 'object' &&
                            !Object.values(lastPackage).some(
                              (value) => value !== undefined && value !== null && value !== ''
                            ))
                        ) {
                          setShowNewForm(true);
                        }

                        form
                          .validateFields([
                            ['packages', lastIndex, 'packageType'],
                            ['packages', lastIndex, 'quantity'],
                            ['packages', lastIndex, 'totalWeight'],
                            ['packages', lastIndex, 'length'],
                            ['packages', lastIndex, 'width'],
                            ['packages', lastIndex, 'height'],
                            ['packages', lastIndex, 'imageUrl'],
                          ])
                          .then(() => {
                            const lastIndex = fields.length - 1;
                            const hasValue = packageHasValue(lastIndex);

                            if (hasValue) {
                              add();
                              setEditingIndex(fields.length);
                            } else {
                              setEditingIndex(lastIndex);
                            }

                            setShowNewForm(true);
                          })
                          .catch((err) => {
                            console.error('Validation failed:', err);
                          });
                      }
                    }}
                    className="bg-[#2E3A8C] text-white hover:!bg-[#2E3A8C] hover:opacity-90 rounded-md p-2 py-4"
                  >
                    {t('orderEntryForms.packageDetails.addMorePackage')}
                  </Button>
                </div>
            </>
          )}
        </Form.List>
      </div>}
    </Form>
  );
};

export default PackageDetailsForm;
