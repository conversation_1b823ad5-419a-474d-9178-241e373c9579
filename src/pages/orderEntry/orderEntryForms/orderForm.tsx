import { useState, useEffect } from 'react';
import { <PERSON>, Col, Collapse, Button, Form, Checkbox, Select } from 'antd';
import {
  PickupAddressForm,
  DeliveryAddressForm,
  TimingForm,
  PackageDetailsForm,
  AdditionalDetailsForm,
  AvailableDeliveryServices,
  PaymentSummary,
} from './index';
import './addressForms.css';
import {
  CollapseDownIcon,
  CollapseUpIcon,
  EmailOutlinedIcon,
  PhoneOutlinedIcon,
  StarForSelectUnfilledIcon,
} from '@/assets';
import { addressServiceHook } from '@/api/address/useAddress';
import { GetAddressDto } from '@/api/address/address.types';
import { scheduleServiceHook } from '@/api/service/useService';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import PaymentsPage from '../payments';
import { IGetAvailableServices } from '@/api/service/service.types';
import { ordersServiceHook } from '@/api/order/useOrders';
import { IModifier, IOrder, IOrderDetailsForPlaceOrder, IPackages } from '@/api/order/order.types';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { EditPopupWhiteIcon } from '@/assets/icons/editPopupIconWhite';
import CustomModal from '@/components/common/modal/CustomModal';
import AddNewLocationComponent from './addNewLocation';
import { packageServiceHook } from '@/api/packages/usePackages';
import { IPackage, IPackagesList } from '@/api/packages/packages.types';
import { FavoriteAddressIcon } from '@/assets/icons/favouriteAddressIcon';
import { ModifiersConfigurationType } from '@/types/enums/ModifiersConfigurationType';
import { Switch } from 'antd';
import dayjs from 'dayjs';
import { useConfig } from '@/contexts/ConfigContext';

const { Panel } = Collapse;

const OrderForm = () => {
  const {config} = useConfig();
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [timingForm] = Form.useForm();

  const [addNewLocationForm] = Form.useForm();
  const [additionalDetailsForm] = Form.useForm();
  const [selectedDeliveryService, setSelectedDeliveryService] =
    useState<IGetAvailableServices | null>(null);
  const [containerHeight, setContainerHeight] = useState('calc(100vh - 180px)');
  const [packageFormDetails, setPackageFormDetails] = useState<IPackages[]>([]);
  const [isAbleToPlaceOrder, setIsAbleToPlaceOrder] = useState(false);
  const [packageDetailsForm] = Form.useForm();
  const [isStripeRequired, setIsStripeRequired] = useState(true);
  const { data: packagesList } = packageServiceHook.useList<IPackagesList>();
  const [selectedDateAndTime, setSelectedDateAndTime] = useState('');
  const [pickUpAddressForm] = Form.useForm();
  const [deliveryAddressForm] = Form.useForm();
  const [isAllFilledUp, setIsAllFilledUp] = useState(false);
  const {
    data: allAddressesList,
    refetch,
    isRefetching,
  } = addressServiceHook.useEntities('no-pagination');
  const [availableServices, setAvailableServices] = useState<IGetAvailableServices[]>();
  const [selectedAddresses, setSelectedAddresses] = useState<{
    pickupAddress: GetAddressDto | null;
    deliveryAddress: GetAddressDto | null;
  }>({
    pickupAddress: null,
    deliveryAddress: null,
  });
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasFirstCompleteSubmission, setHasFirstCompleteSubmission] = useState(false);
  const [isOpen, setIsOpen] = useState<{ forPickUp: boolean; forDelivery: boolean }>({
    forPickUp: false,
    forDelivery: false,
  });
  const [isAddNewAddress, setIsAddNewAddress] = useState<{
    isForPickup: boolean;
    isForDelivery: boolean;
  }>({ isForPickup: false, isForDelivery: false });
  const [allAddresses, setAllAddresses] = useState<GetAddressDto[]>([]);
  const [activeKeys, setActiveKeys] = useState<{ pickUpAddress: string; deliveryAddress: string }>({
    pickUpAddress: '',
    deliveryAddress: '',
  });
  const [isFieldChanged, setIsFieldChanged] = useState<{
    pickUpAddress: false;
    deliveryAddress: false;
  }>({
    pickUpAddress: false,
    deliveryAddress: false,
  });
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const { refetch: refetchOrders } = ordersServiceHook.useList();
  const { data: orderDetailsById } = ordersServiceHook.useEntity(id as string, {
    enabled: isEditMode,
  });
  const navigate = useNavigate();
  const [availablePackages, setAvailablePackages] = useState<IPackage[]>();
  const [isFieldsEditable, setIsFieldsEditable] = useState(true);
  const pickupAddressFormValues = pickUpAddressForm.getFieldsValue();
  useEffect(() => {
    if (pickupAddressFormValues.companyName) {
      setSelectedAddresses({
        ...selectedAddresses,
        pickupAddress: { ...pickupAddressFormValues, name: pickupAddressFormValues.contactName },
      });
    }
  }, []);
  useEffect(() => {
    if (orderDetailsById !== undefined && id !== undefined) {
      const orderPlaceTime = orderDetailsById?.createdAt
      const isTimePassed = dayjs(orderPlaceTime).add(config.allowEditOrderTill as number, 'minute').isBefore(dayjs());
      if (isTimePassed && orderDetailsById?.status === 'Submitted' && id) {
        setIsFieldsEditable(false)
      }
      else {
        setIsFieldsEditable(true)
      };
    }
  }, [orderDetailsById, id])
  useEffect(() => {
    if (packagesList) {
      const formattedPackages = packagesList?.packages.map((item: IPackage) => ({
        label: item.name,
        value: item.id,
        capabilities: item.capabilities,
        description: item.description,
        dimensionsRequired: item.dimensionsRequired,
        id: item.id,
        maxVolume: item.maxVolume,
        maxWeight: item.maxWeight,
        name: item.name,
        requiresInsurance: item.requiresInsurance,
        requiresSignature: item.requiresSignature,
        specialHandlingInstructions: item.specialHandlingInstructions,
        status: item.status,
        weightRequired: item.weightRequired,
        length: item.length,
        width: item.width,
        height: item.height,
        metadata: {
          isPrimary: item.metadata?.isPrimary,
        },
      }));
      setAvailablePackages(formattedPackages as unknown as IPackage[]);
    }
  }, [packagesList]);
  useEffect(() => {
    const updateHeight = () => {
      const headerHeight = 120;
      const footerHeight = 53;
      const windowHeight = window.innerHeight;
      setContainerHeight(`calc(${windowHeight}px - ${headerHeight}px - ${footerHeight}px)`);
    };
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => {
      window.removeEventListener('resize', updateHeight);
    };
  }, []);
  const getServicesByPickUpDate = scheduleServiceHook.useCreate({});
  const fetchServices = async (
    dateAndTime: string,
    pricing: boolean,
    orderDetails: IOrderDetailsForPlaceOrder
  ) => {
    const response = await getServicesByPickUpDate.mutateAsync({
      pickupDate: dateAndTime,
      includePricing: pricing,
      order: orderDetails,
    });
    if (response) {
      setAvailableServices(response?.data);
      setSelectedDeliveryService(null);
    }
  };
  useEffect(() => {
    const currentDateAndTime = new Date().toISOString();

    if (isInitialLoad) {
      fetchServices(currentDateAndTime, false, {} as IOrderDetailsForPlaceOrder);
      setIsInitialLoad(false);
    }
  }, []);
  useEffect(() => {
    const declaredPrice = additionalDetailsForm.getFieldValue('insuranceAmount');
    const hasRequiredFields =
      selectedAddresses.pickupAddress !== null &&
      selectedAddresses.deliveryAddress !== null &&
      selectedDateAndTime !== '';

    if (!hasRequiredFields) {
      return;
    }

    const orderJSON = {
      collectionAddressId: selectedAddresses.pickupAddress?.id as string,
      deliveryAddressId: selectedAddresses.deliveryAddress?.id as string,
      scheduledCollectionTime: selectedDateAndTime,
      totalItems: packageFormDetails?.length,
      declaredPrice: declaredPrice,
      items: packageFormDetails.map((item: IPackages) => ({
        ...item,
        imageUrl: item?.imageUrl?.file,
      })),
    };
    if (!hasFirstCompleteSubmission && hasRequiredFields) {
      fetchServices(selectedDateAndTime, true, orderJSON);
      setHasFirstCompleteSubmission(true);
    } else if (hasFirstCompleteSubmission) {
      fetchServices(selectedDateAndTime, true, orderJSON);
    }
  }, [
    selectedAddresses,
    selectedDateAndTime,
    hasFirstCompleteSubmission,
    packageFormDetails,
  ]);
  useEffect(() => {
    const hasRequiredFields =
      selectedAddresses.pickupAddress &&
      selectedAddresses.deliveryAddress &&
      selectedDateAndTime !== '' &&
      packageFormDetails.length > 0 &&
      selectedDeliveryService !== null;

    const canPlaceOrder = isStripeRequired ? hasRequiredFields && isAllFilledUp : hasRequiredFields;

    setIsAbleToPlaceOrder(canPlaceOrder ?? false);
  }, [
    selectedAddresses.pickupAddress,
    selectedAddresses.deliveryAddress,
    selectedDateAndTime,
    packageFormDetails,
    isAllFilledUp,
    selectedDeliveryService,
    isStripeRequired,
  ]);

  const createDraftMutation = ordersServiceHook.useCreateByEntity('draft', {
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('orderEntryForms.address.draftCreatedSuccessfully'),
      });
    },
  });

  const handleSaveAsDraft = async () => {
    const dataToCreateDraft = {
      id : id,
      referenceNumber: '',
      collectionAddressId: selectedAddresses.pickupAddress?.id as string,
      collectionContactName: selectedAddresses.pickupAddress?.name as string,
      collectionInstructions: selectedAddresses.pickupAddress?.notes as string,
      deliveryAddressId: selectedAddresses.deliveryAddress?.id as string,
      deliveryContactName: selectedAddresses.deliveryAddress?.name as string,
      deliveryInstructions: selectedAddresses.deliveryAddress?.notes as string,
      internalNotes: additionalDetailsForm.getFieldValue('specialInstruction') as string,
      comments: '',
      items:
        packageFormDetails?.length > 0
          ? packageFormDetails.map((item: IPackages) => ({
            ...item,
            totalWeight: item?.totalWeight,
            imageUrl: item?.imageUrl?.file,
          }))
          : [],
      isCod: true,
      codAmount: additionalDetailsForm.getFieldValue('amount'),
      isInsurance: true,
      declaredValue: additionalDetailsForm.getFieldValue('insuranceAmount'),
    };
    await createDraftMutation.mutateAsync(dataToCreateDraft as IOrder);
  };

  const handleDiscardOrder = () => {
    customAlert.error({
      title: t('orderEntryForms.discardOrder.title'),
      message: t('orderEntryForms.discardOrder.message'),
      firstButtonTitle: t('orderEntryForms.discardOrder.discardButton'),
      secondButtonTitle: t('common.buttons.cancel'),
      firstButtonFunction: () => {
        window.location.reload();
      },
      secondButtonFunction: () => {
        customAlert.destroy();
      },
    });
  };
  const notificationManager = useNotificationManager();
  const updateOrderMutation = ordersServiceHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Order updated successfully',
      })
      navigate(ROUTES.ORDER.LISTING);
      refetchOrders();
    }
  });
  const createOrderMutation = ordersServiceHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Order placed successfully',
      });
      navigate(ROUTES.ORDER.LISTING);
      refetchOrders();
    },
  });
  const handleSelect = (
    selectedAddress: GetAddressDto,
    type: 'pickupAddress' | 'deliveryAddress'
  ) => {
    const phoneNumberFormat = selectedAddress?.phoneNumber.toString();
    if (type === 'pickupAddress') {
      pickUpAddressForm.setFieldsValue({
        contactName: selectedAddress.name,
        companyName: selectedAddress.companyName,
        email: selectedAddress.email,
        phone: phoneNumberFormat,
        phoneExtension: selectedAddress.phoneExtension,
        addressLine1: selectedAddress.addressLine1,
        phoneCountryCode: selectedAddress.countryCode,
        addressLine2: selectedAddress.addressLine2,
        city: selectedAddress.city,
        province: selectedAddress.province,
        country: selectedAddress.country,
        postalCode: selectedAddress.postalCode,
        id: selectedAddress.id,
      });
    } else {
      deliveryAddressForm.setFieldsValue({
        contactName: selectedAddress.name,
        companyName: selectedAddress.companyName,
        email: selectedAddress.email,
        phone: phoneNumberFormat,
        phoneExtension: selectedAddress.phoneExtension,
        addressLine1: selectedAddress.addressLine1,
        phoneCountryCode: selectedAddress.countryCode,
        addressLine2: selectedAddress.addressLine2,
        city: selectedAddress.city,
        province: selectedAddress.province,
        country: selectedAddress.country,
        postalCode: selectedAddress.postalCode,
        id: selectedAddress.id,
      });
    }

    setIsOpen({ forPickUp: false, forDelivery: false });
    setSelectedAddresses((prev) => ({
      ...prev,
      [type]: selectedAddress,
    }));

  };

  const createCustomerAddressMutation = addressServiceHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successCreate'),
      });
      refetch();

      setActiveKeys({
        pickUpAddress: '',
        deliveryAddress: '',
      });
      setIsAddNewAddress({
        isForPickup: false,
        isForDelivery: false,
      });
    },
  });
  const updateCustomerAddressMutation = addressServiceHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successUpdate'),
      });
      refetch();
      setActiveKeys({
        pickUpAddress: '',
        deliveryAddress: '',
      });
    },
  });

  const favouriteAddressForPickup = addressServiceHook.useUpdate({
    onSuccess: (updatedAddress) => {
      notificationManager.success({
        message: t('orderEntryForms.address.success'),
        description: t('orderEntryForms.address.addedToFavoriteDelivery'),
      });
      refetch();
      if (selectedAddresses.pickupAddress?.id === updatedAddress.id) {
        setSelectedAddresses((prev) => ({
          ...prev,
          pickupAddress: updatedAddress,
        }));
      }
    },
  });
  const onFavouritePickupAddress = async (address: GetAddressDto) => {
    const dataToUpdate = {
      ...address,
      isFavoriteForPickup: !address.isFavoriteForPickup,
    };
    await favouriteAddressForPickup.mutateAsync({ id: address.id, data: dataToUpdate });
  };

  const favouriteAddressForDelivery = addressServiceHook.useUpdate({
    onSuccess: (updatedAddress) => {
      notificationManager.success({
        message: t('orderEntryForms.address.success'),
        description: t('orderEntryForms.address.addedToFavoriteDelivery'),
      });
      refetch();
      if (selectedAddresses.deliveryAddress?.id === updatedAddress.id) {
        setSelectedAddresses((prev) => ({
          ...prev,
          deliveryAddress: updatedAddress,
        }));
      }
    },
  });

  const onFavouriteDeliveryAddress = async (address: GetAddressDto) => {
    const dataToUpdate = {
      ...address,
      isFavoriteForDelivery: !address.isFavoriteForDelivery,
    };
    await favouriteAddressForDelivery.mutateAsync({ id: address.id, data: dataToUpdate });
  };

  const saveNewAddress = async () => {
    const values = await pickUpAddressForm.getFieldsValue();

    await pickUpAddressForm.validateFields();

    await createCustomerAddressMutation.mutateAsync({
      ...values,
      name: values.contactName,
      countryCode: values.phoneCountryCode,
      phoneNumber: values.phone,
    });
  };
  const saveNewLocation = async (type: 'pickupAddress' | 'deliveryAddress') => {
    const values = await addNewLocationForm.getFieldsValue();
    await addNewLocationForm.validateFields();
    const response = await createCustomerAddressMutation.mutateAsync({
      ...values,
      countryCode: values.countryCode,
      phoneNumber: values.phoneNumber,
    });
    if (type === 'deliveryAddress') {
      deliveryAddressForm.setFieldsValue({
        ...values,
        contactName: response.name,
        phoneCountryCode: response.countryCode,
        phone: response.phoneNumber,
      });
      setSelectedAddresses((prev) => ({
        ...prev,
        deliveryAddress: response,
      }));
    } else {
      pickUpAddressForm.setFieldsValue({
        ...values,
        contactName: response.name,
        phoneCountryCode: response.countryCode,
        phone: response.phoneNumber,
      });
      setSelectedAddresses((prev) => ({
        ...prev,
        pickupAddress: response,
      }));
    }
  };

  useEffect(() => {
    setAllAddresses(allAddressesList?.data as GetAddressDto[]);
    if (!isEditMode && !selectedAddresses.pickupAddress && allAddressesList?.data) {
      const defaultPickup = allAddressesList.data.find((addr) => addr.isDefaultForPickup);
      if (defaultPickup) {
        setSelectedAddresses((prev) => ({
          ...prev,
          pickupAddress: defaultPickup,
        }));
        pickUpAddressForm.setFieldsValue({
          ...defaultPickup,
          contactName: defaultPickup.name,
          phoneCountryCode: defaultPickup.countryCode,
          phone: defaultPickup.phoneNumber,
        });
      }
    }
  }, [allAddressesList?.data]);
  const editExistingAddress = async () => {
    const values = await pickUpAddressForm.getFieldsValue();
    await pickUpAddressForm.validateFields();
    await updateCustomerAddressMutation.mutateAsync({
      id: values.id as string,
      data: {
        ...values,
        name: values.contactName,
        countryCode: values.phoneCountryCode,
        phoneNumber: values.phone,
      },
    });
  };

  const footer = (
    <>
      <Button
        onClick={() =>
          setIsAddNewAddress({
            isForPickup: false,
            isForDelivery: false,
          })
        }
      >
        Cancel
      </Button>
      <Button
        form="new-address-form"
        type="primary"
        htmlType="submit"
        onClick={() =>
          saveNewLocation(isAddNewAddress.isForPickup ? 'pickupAddress' : 'deliveryAddress')
        }
      >
        Add
      </Button>
    </>
  );

  const onFinish = async () => {
    const validatePickUpAddressForm = await pickUpAddressForm.validateFields();
    const validateDeliveryAddressForm = await deliveryAddressForm.validateFields();
    const validatePackageDetailsForm = await packageDetailsForm.validateFields();
    if (validatePickUpAddressForm && validateDeliveryAddressForm && validatePackageDetailsForm) {
      const placeOrderData = {
        orderId: id,
        collectionAddressId: selectedAddresses.pickupAddress?.id as string,
        collectionContactName: selectedAddresses.pickupAddress?.name as string,
        collectionInstructions: selectedAddresses.pickupAddress?.notes as string,
        collectionSignatureRequired: false,
        scheduledCollectionTime: selectedDateAndTime,
        deliveryAddressId: selectedAddresses.deliveryAddress?.id as string,
        deliveryContactName: selectedAddresses.deliveryAddress?.name as string,
        deliveryInstructions: selectedAddresses.deliveryAddress?.notes as string,
        deliverySignatureRequired: false,

        priceSetId: selectedDeliveryService?.id as string,
        internalNotes: additionalDetailsForm.getFieldValue('specialInstruction') as string,
        comments: '',
        items:
          packageFormDetails?.length > 0
            ? packageFormDetails.map((item: IPackages) => ({
              ...item,
              imageUrl: item?.imageUrl?.file,
            }))
            : [],
        isCod: true,
        codAmount: additionalDetailsForm.getFieldValue('amount'),
        isInsurance: true,
        declaredValue: additionalDetailsForm.getFieldValue('insuranceAmount'),
        calculationResult: {
          priceSetId: selectedDeliveryService?.id as string,
          selectedModifiers: modifiersByUser.selectedModifiersByUser,
          modifiersTotal: modifiersTotal,
          disSelectedModifiers: modifiersByUser.disSelectedModifiersByUser,
          basePrice: selectedDeliveryService?.pricing?.basePrice,
        },
      };
      if (isEditMode && orderDetailsById?.status !== 'Draft') {
        await updateOrderMutation.mutateAsync({ id: id as string, data: placeOrderData });
      } else { await createOrderMutation.mutateAsync(placeOrderData); }

    }
  };
  useEffect(() => {

    if (isEditMode && orderDetailsById && allAddressesList?.data) {
      if (orderDetailsById?.priceSetId) {
        const selectedDeliveryServiceInfo = availableServices?.find((service) => service.id === orderDetailsById.priceSetId);
        if (selectedDeliveryServiceInfo) {
          setSelectedDeliveryService(selectedDeliveryServiceInfo);
        }
      }

      additionalDetailsForm.setFieldsValue({
        specialInstruction: orderDetailsById?.internalNotes,
        amount: orderDetailsById?.codAmount,
        isCod: orderDetailsById?.codAmount ? true : false,
      })

    }
  }, [
    isEditMode,
    orderDetailsById,
    allAddressesList,
    pickUpAddressForm,
    deliveryAddressForm,
    availableServices,
    additionalDetailsForm
  ]);



  useEffect(() => {
    if (isEditMode && orderDetailsById) {

      if (orderDetailsById.items && Array.isArray(orderDetailsById.items)) {
        setPackageFormDetails(orderDetailsById.items);
        packageDetailsForm.setFieldsValue({
          packages: { ...orderDetailsById.items },
        });
      }
      const pickupAddress = allAddressesList?.data.find(
        (address) => address.id === orderDetailsById?.collectionAddressId
      );
      const deliveryAddress = allAddressesList?.data.find(
        (address) => address.id === orderDetailsById?.deliveryAddressId
      );

      if (orderDetailsById?.scheduledCollectionTime) {
        setSelectedDateAndTime(orderDetailsById.scheduledCollectionTime);
      }
      if (pickupAddress) {
        setSelectedAddresses((prev) => ({ ...prev, pickupAddress }));
        const phoneNumberFormat = pickupAddress.phoneNumber.toString();
        pickUpAddressForm.setFieldsValue({
          contactName: pickupAddress.name,
          companyName: pickupAddress.companyName,
          email: pickupAddress.email,
          phone: phoneNumberFormat,
          phoneExtension: pickupAddress.phoneExtension,
          addressLine1: pickupAddress.addressLine1,
          phoneCountryCode: pickupAddress.countryCode,
          addressLine2: pickupAddress.addressLine2,
          city: pickupAddress.city,
          province: pickupAddress.province,
          country: pickupAddress.country,
          postalCode: pickupAddress.postalCode,
          id: pickupAddress.id,
        });
      }

      if (deliveryAddress) {
        setSelectedAddresses((prev) => ({ ...prev, deliveryAddress }));
        const phoneNumberFormat = deliveryAddress.phoneNumber.toString();
        deliveryAddressForm.setFieldsValue({
          contactName: deliveryAddress.name,
          companyName: deliveryAddress.companyName,
          email: deliveryAddress.email,
          phone: phoneNumberFormat,
          phoneExtension: deliveryAddress.phoneExtension,
          addressLine1: deliveryAddress.addressLine1,
          phoneCountryCode: deliveryAddress.countryCode,
          addressLine2: deliveryAddress.addressLine2,
          city: deliveryAddress.city,
          province: deliveryAddress.province,
          country: deliveryAddress.country,
          postalCode: deliveryAddress.postalCode,
          id: deliveryAddress.id,
        });
      }
    }
  }, [orderDetailsById])




  interface ISelectionByUser {
    selectedModifiersByUser: IModifier[];
    disSelectedModifiersByUser: IModifier[];
  }
  const [modifierSelections, setModifierSelections] = useState<{ [id: string]: boolean }>({});
  const [modifiersByUser, setModifiersByUser] = useState<ISelectionByUser>({
    selectedModifiersByUser: [],
    disSelectedModifiersByUser: [],
  });

  useEffect(() => {
    if (selectedDeliveryService?.pricing?.modifiers) {
      const initialSelections: { [id: string]: boolean } = {};
      selectedDeliveryService?.pricing?.modifiers
        ?.filter((mod: IModifier) => mod.configuration === ModifiersConfigurationType.SELECTED)
        .forEach((modifier: IModifier) => {
          initialSelections[modifier.id] = true;
        });
      setModifierSelections(initialSelections);
      const selectedModifiers = selectedDeliveryService.pricing.modifiers.filter(
        (modifier: IModifier) => modifier.configuration !== ModifiersConfigurationType.REQUIRED
      );
      setModifiersByUser({ ...modifiersByUser, selectedModifiersByUser: selectedModifiers });
    }
  }, [selectedDeliveryService]);
  useEffect(() => {
    if (!selectedDeliveryService?.pricing?.modifiers) return;
    const allModifiers = selectedDeliveryService.pricing.modifiers;
    const selected = allModifiers.filter(
      (mod) =>
        mod.configuration === ModifiersConfigurationType.REQUIRED || modifierSelections[mod.id]
    );
    const disselected = allModifiers.filter(
      (mod) =>
        !modifierSelections[mod.id] && mod.configuration !== ModifiersConfigurationType.REQUIRED
    );
    setModifiersByUser({
      selectedModifiersByUser: selected,
      disSelectedModifiersByUser: disselected,
    });
  }, [modifierSelections, selectedDeliveryService]);
  const handleModifierChange = (modifierId: string) => {
    setModifierSelections((prev) => {
      let notSelectedToSelected = Object.keys(prev).filter((key) => !prev[key]);
      let selectedToNotSelected: { [id: string]: boolean } = {};
      if (notSelectedToSelected.includes(modifierId)) {
        return {
          ...prev,
          [modifierId]: true,
        };
      } else {
        selectedToNotSelected = {
          ...prev,
          [modifierId]: !prev[modifierId],
        };
      }
      return selectedToNotSelected;
    });
  };
  const basePrice = selectedDeliveryService?.pricing?.basePrice || 0;
  const modifiers = selectedDeliveryService?.pricing?.modifiers || [];

  const subTotal =
    basePrice +
    modifiers
      .filter((m: IModifier) => m.configuration === ModifiersConfigurationType.REQUIRED)
      .reduce((sum: number, m: IModifier) => sum + (m.amount || 0), 0);

  const additionalServicesTotal = modifiers
    .filter((m: IModifier) => modifierSelections[m.id])
    .reduce((sum: number, m: IModifier) => sum + (m.amount || 0), 0);

  const modifiersTotal = modifiers
    .filter(
      (m: IModifier) =>
        m.configuration === ModifiersConfigurationType.REQUIRED || modifierSelections[m.id]
    )
    .reduce((sum: number, m: IModifier) => sum + (m.amount || 0), 0);

  const tax = 0;
  const computedTotalAmount = subTotal + additionalServicesTotal + tax;
  return (
    <>
      <CustomModal
        modalTitle="Add as new address"
        modalDescription="Fill the details to add new address"
        open={isAddNewAddress.isForDelivery || isAddNewAddress.isForPickup}
        onCancel={() =>
          setIsAddNewAddress({
            isForPickup: false,
            isForDelivery: false,
          })
        }
        footer={footer}
      >
        <AddNewLocationComponent form={addNewLocationForm} />
      </CustomModal>
      <Form form={form} onFinish={onFinish} layout="vertical" className="order-form pl-5 py-6">
        <div className="order-form-container" style={{ height: containerHeight }}>
          <Row gutter={[24, 0]} className="order-form-row">
            <Col xs={24} lg={16} className="forms-column">
              <div className="scrollable-forms-container">
                <Collapse
                  collapsible="icon"
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span
                        onClick={() =>
                          setActiveKeys({
                            ...activeKeys,
                            pickUpAddress: isActive ? '' : '1',
                          })
                        }
                        className="text-[#2D3484] font-semibold text-[14px] bg-primary-25"
                      >
                        {isActive ? <CollapseDownIcon /> : null}
                      </span>
                    );
                  }}
                  activeKey={activeKeys.pickUpAddress}
                  className="form-collapse"
                >
                  <Panel
                    key="1"
                    header={
                      <div className="flex flex-col">
                        <span className="bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb]">
                          {t('orderEntryForms.pickupAddress.title')}
                        </span>

                        {selectedAddresses.pickupAddress && (
                          <div
                            onClick={(e) => e.stopPropagation()}
                            className="flex flex-col m-4 w-[100%] gap-1 p-4 rounded-lg border border-gray-200 shadow bg-primary-25"
                            tabIndex={0}
                            aria-label="Pickup address card"
                          >
                            <div className="flex justify-end align-center  ">
                              <Button
                                onClick={() => {
                                  if (activeKeys.pickUpAddress?.length === 0) {
                                    setActiveKeys({ ...activeKeys, pickUpAddress: '1' });
                                  }
                                }}
                                className="text-xs px-3 py-1 border border-gray-300 hover:!border-gray-300 rounded"
                                aria-label="Edit address"
                                tabIndex={0}
                              >
                                <EditPopupWhiteIcon /> Edit
                              </Button>
                            </div>
                            <span className="font-semibold text-base">
                              {selectedAddresses.pickupAddress.companyName}
                            </span>
                            <span className="flex gap-2 items-center font-semibold text-base">
                              {selectedAddresses.pickupAddress.name} <PhoneOutlinedIcon />
                              {selectedAddresses.pickupAddress.phoneNumber}
                              {selectedAddresses.pickupAddress.phoneExtension &&
                                ` ext. ${selectedAddresses.pickupAddress.phoneExtension}`}
                              <span className="ml-2">
                                <EmailOutlinedIcon />
                              </span>{' '}
                              {selectedAddresses.pickupAddress.email}
                            </span>
                            <span className="text-base">
                              {selectedAddresses.pickupAddress.addressLine1},{' '}
                              {selectedAddresses.pickupAddress.addressLine2},{' '}
                              {selectedAddresses.pickupAddress.city},{' '}
                              {selectedAddresses.pickupAddress.province},{' '}
                              {selectedAddresses.pickupAddress.country},{' '}
                              {selectedAddresses.pickupAddress.postalCode}
                            </span>
                          </div>
                        )}
                        <div
                          className="flex w-full my-3 gap-3  mx-6 justify-between"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Form.Item label="Search Location" className="w-5/6 mb-0">
                            <Select
                              disabled={!isFieldsEditable}
                              onSearch={(value) => {
                                setAllAddresses(
                                  allAddresses?.filter((address) =>
                                    address.name.toLowerCase().includes(value.toLowerCase())
                                  ) as GetAddressDto[]
                                );
                              }}
                              showSearch
                              onSelect={(value) => {
                                setAllAddresses(
                                  allAddresses?.filter((address) =>
                                    address.name.toLowerCase().includes(value.toLowerCase())
                                  ) as GetAddressDto[]
                                );
                              }}
                              open={isOpen.forPickUp}
                              placeholder="Search Contact, Company or Address"
                              className="w-full rounded-lg h-[40px]"
                              aria-label="Search location"
                              tabIndex={0}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                typeof option?.children === 'string' &&
                                (option.children as string)
                                  .toLowerCase()
                                  .includes(input.toLowerCase())
                              }
                              onDropdownVisibleChange={(value) =>
                                setIsOpen({ ...isOpen, forPickUp: value })
                              }
                              dropdownRender={() => {
                                const favoriteAddresses = allAddresses?.filter(
                                  (address) => address.isFavoriteForPickup
                                );
                                const otherAddresses = allAddresses?.filter(
                                  (address) => !address.isFavoriteForPickup
                                );

                                return (
                                  <div className="max-h-[300px] overflow-y-auto">
                                    {favoriteAddresses?.length > 0 && (
                                      <div className="py-2 px-3">
                                        <span className="font-bold text-sm text-primary-600">
                                          Favourite
                                        </span>
                                        {favoriteAddresses.map((address) => {
                                          const isSelected =
                                            selectedAddresses.pickupAddress?.id === address.id;
                                          return (
                                            <div
                                              key={address.id}
                                              className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                                              onClick={() => handleSelect(address, 'pickupAddress')}
                                              tabIndex={0}
                                            >
                                              <span>
                                                {address.name} ({address.companyName})
                                              </span>
                                              <span
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onFavouritePickupAddress(address);
                                                }}
                                                className="cursor-pointer"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                  if (e.key === 'Enter' || e.key === ' ') {
                                                    e.stopPropagation();
                                                    onFavouritePickupAddress(address);
                                                  }
                                                }}
                                              >
                                                {address.isFavoriteForPickup ? (
                                                  <FavoriteAddressIcon />
                                                ) : (
                                                  <StarForSelectUnfilledIcon />
                                                )}
                                              </span>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    )}

                                    {otherAddresses?.length > 0 && (
                                      <div className="py-2 px-3">
                                        <span className="font-bold text-sm text-primary-600">
                                          All Address
                                        </span>
                                        {otherAddresses.map((address) => {
                                          const isSelected =
                                            selectedAddresses.pickupAddress?.id === address.id;
                                          return (
                                            <div
                                              key={address.id}
                                              className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                                              onClick={() => handleSelect(address, 'pickupAddress')}
                                              tabIndex={0}
                                              onKeyDown={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') {
                                                  handleSelect(address, 'pickupAddress');
                                                }
                                              }}
                                            >
                                              <span>
                                                {address.name} ({address.companyName})
                                              </span>
                                              <span
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onFavouritePickupAddress(address);
                                                }}
                                                className="cursor-pointer"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                  if (e.key === 'Enter' || e.key === ' ') {
                                                    e.stopPropagation();
                                                    onFavouritePickupAddress(address);
                                                  }
                                                }}
                                              >
                                                {address.isFavoriteForPickup ? (
                                                  <FavoriteAddressIcon />
                                                ) : (
                                                  <StarForSelectUnfilledIcon />
                                                )}
                                              </span>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    )}

                                    {!allAddresses ||
                                      (allAddresses?.length === 0 && (
                                        <span className="h-[35px] mx-2">
                                          No Addresses are available to assign
                                        </span>
                                      ))}
                                  </div>
                                );
                              }}
                            ></Select>
                          </Form.Item>
                          <Form.Item className="w-1/6 mb-0 flex" label=" ">
                            <Button
                              onClick={() =>
                                setIsAddNewAddress({ isForPickup: true, isForDelivery: false })
                              }
                              className="w-full bg-primary-700 hover:bg-primary-800 text-white rounded-lg py-2 px-4 font-[500]"
                              aria-label="Add new location"
                              type="primary"
                              size="large"
                              disabled={!isFieldsEditable}
                            >
                              + Add new location
                            </Button>
                          </Form.Item>
                        </div>
                      </div>
                    }
                  >
                    <PickupAddressForm
                      selectedPickupAddress={selectedAddresses.pickupAddress}
                      refetch={refetch}
                      isRefetching={isRefetching}
                      form={pickUpAddressForm}
                      setIsFieldChanged={setIsFieldChanged}
                      isFieldChanged={isFieldChanged}
                      isAbleToEdit={isFieldsEditable}
                    />
                  </Panel>
                  {isFieldChanged.pickUpAddress && activeKeys.pickUpAddress?.includes('1') && (
                    <div className="flex w-full bg-primary-25 h-[60px] font-[600] border-t border-[#e5e7eb] justify-end p-2 gap-3">
                      <Button disabled={!isFieldsEditable}
                        onClick={saveNewAddress} className="text-[#2D3484] h-[40px] ">
                        Save as new location
                      </Button>
                      <Button
                        disabled={!isFieldsEditable}

                        onClick={editExistingAddress}
                        className="bg-primary-600 text-white h-[40px] hover:!bg-primary-600 hover:!text-white"
                      >
                        Save
                      </Button>
                    </div>
                  )}
                </Collapse>

                <Collapse
                  collapsible="icon"
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span
                        onClick={() =>
                          setActiveKeys({
                            ...activeKeys,
                            deliveryAddress: isActive ? '' : '1',
                          })
                        }
                        className="text-[#2D3484] font-semibold text-[14px] bg-primary-25"
                      >
                        {isActive ? <CollapseDownIcon /> : null}
                      </span>
                    );
                  }}
                  activeKey={activeKeys.deliveryAddress}
                  className="form-collapse mt-4"
                >
                  <Panel
                    key="1"
                    header={
                      <div className="flex flex-col">
                        <span
                          className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                        >
                          {t('orderEntryForms.deliveryAddress.title')}
                        </span>
                        {selectedAddresses.deliveryAddress && (
                          <div
                            onClick={(e) => e.stopPropagation()}
                            className="flex flex-col m-4 w-[100%] gap-1 p-4 rounded-lg border border-gray-200 shadow bg-primary-25"
                            tabIndex={0}
                            aria-label="Pickup address card"
                          >
                            <div className="flex justify-end align-center  ">
                              <Button
                                onClick={() => {
                                  if (activeKeys.deliveryAddress?.length === 0) {
                                    setActiveKeys({ ...activeKeys, deliveryAddress: '1' });
                                  }
                                }}
                                className="text-xs px-3 py-1 border border-gray-300 hover:!border-gray-300 rounded"
                                aria-label="Edit address"
                                tabIndex={0}
                              >
                                <EditPopupWhiteIcon /> Edit
                              </Button>
                            </div>
                            <span className="font-semibold text-base">
                              {selectedAddresses.deliveryAddress.companyName}
                            </span>
                            <span className="flex gap-2 items-center font-semibold text-base">
                              {selectedAddresses.deliveryAddress.name} <PhoneOutlinedIcon />
                              {selectedAddresses.deliveryAddress.phoneNumber}
                              {selectedAddresses.deliveryAddress.phoneExtension &&
                                ` ext. ${selectedAddresses.deliveryAddress.phoneExtension}`}
                              <span className="ml-2">
                                <EmailOutlinedIcon />
                              </span>{' '}
                              {selectedAddresses.deliveryAddress.email}
                            </span>
                            <span className="text-base">
                              {selectedAddresses.deliveryAddress.addressLine1},{' '}
                              {selectedAddresses.deliveryAddress.addressLine2},{' '}
                              {selectedAddresses.deliveryAddress.city},{' '}
                              {selectedAddresses.deliveryAddress.province},{' '}
                              {selectedAddresses.deliveryAddress.country},{' '}
                              {selectedAddresses.deliveryAddress.postalCode}
                            </span>
                          </div>
                        )}
                        <div
                          className="flex w-full my-3 gap-3  mx-6 justify-between"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Form.Item label="Search Location" className="w-5/6 mb-0">
                            <Select
                              disabled={!isFieldsEditable}

                              onSearch={(value) => {
                                setAllAddresses(
                                  allAddresses?.filter((address) =>
                                    address.name.toLowerCase().includes(value.toLowerCase())
                                  ) as GetAddressDto[]
                                );
                              }}
                              showSearch
                              onSelect={(value) => {
                                setAllAddresses(
                                  allAddresses?.filter((address) =>
                                    address.name.toLowerCase().includes(value.toLowerCase())
                                  ) as GetAddressDto[]
                                );
                              }}
                              open={isOpen.forDelivery}
                              placeholder="Search Contact, Company or Address"
                              className="w-full rounded-lg h-[40px]"
                              aria-label="Search location"
                              tabIndex={0}
                              optionFilterProp="children"
                              filterOption={(input, option) =>
                                typeof option?.children === 'string' &&
                                (option.children as string)
                                  .toLowerCase()
                                  .includes(input.toLowerCase())
                              }
                              onDropdownVisibleChange={(value) =>
                                setIsOpen({
                                  ...isOpen,
                                  forDelivery: value,
                                })
                              }
                              dropdownRender={() => {
                                const favoriteAddresses = allAddresses?.filter(
                                  (address) => address.isFavoriteForDelivery
                                );
                                const otherAddresses = allAddresses?.filter(
                                  (address) => !address.isFavoriteForDelivery
                                );
                                return (
                                  <div className="max-h-[300px] overflow-y-auto">
                                    {favoriteAddresses?.length > 0 && (
                                      <div className="py-2 px-3">
                                        <span className="font-bold text-sm text-primary-600">
                                          Favourite
                                        </span>
                                        {favoriteAddresses.map((address) => {
                                          const isSelected =
                                            selectedAddresses.deliveryAddress?.id === address.id;
                                          return (
                                            <div
                                              key={address.id}
                                              className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                                              onClick={() =>
                                                handleSelect(address, 'deliveryAddress')
                                              }
                                              tabIndex={0}
                                              onKeyDown={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') {
                                                  handleSelect(address, 'deliveryAddress');
                                                }
                                              }}
                                            >
                                              <span>
                                                {address.name} ({address.companyName})
                                              </span>
                                              <span
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onFavouriteDeliveryAddress(address);
                                                }}
                                                className="cursor-pointer"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                  if (e.key === 'Enter' || e.key === ' ') {
                                                    e.stopPropagation();
                                                    onFavouriteDeliveryAddress(address);
                                                  }
                                                }}
                                              >
                                                {address.isFavoriteForDelivery ? (
                                                  <FavoriteAddressIcon />
                                                ) : (
                                                  <StarForSelectUnfilledIcon />
                                                )}
                                              </span>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    )}

                                    {otherAddresses?.length > 0 && (
                                      <div className="py-2 px-3">
                                        {favoriteAddresses?.length > 0 && (
                                          <span className="font-bold text-sm text-primary-600">
                                            All Address
                                          </span>
                                        )}
                                        {otherAddresses.map((address) => {
                                          const isSelected =
                                            selectedAddresses.deliveryAddress?.id === address.id;
                                          return (
                                            <div
                                              key={address.id}
                                              className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                                              onClick={() =>
                                                handleSelect(address, 'deliveryAddress')
                                              }
                                              tabIndex={0}
                                              onKeyDown={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') {
                                                  handleSelect(address, 'deliveryAddress');
                                                }
                                              }}
                                            >
                                              <span>
                                                {address.name} ({address.companyName})
                                              </span>
                                              <span
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onFavouriteDeliveryAddress(address);
                                                }}
                                                className="cursor-pointer"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                  if (e.key === 'Enter' || e.key === ' ') {
                                                    e.stopPropagation();
                                                    onFavouriteDeliveryAddress(address);
                                                  }
                                                }}
                                              >
                                                {address.isFavoriteForDelivery ? (
                                                  <FavoriteAddressIcon />
                                                ) : (
                                                  <StarForSelectUnfilledIcon />
                                                )}
                                              </span>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    )}

                                    {!allAddresses ||
                                      (allAddresses?.length === 0 && (
                                        <span className="h-[35px] mx-2">
                                          No Addresses are available to assign
                                        </span>
                                      ))}
                                  </div>
                                );
                              }}
                            ></Select>
                          </Form.Item>
                          <Form.Item className="w-1/6 mb-0 flex" label=" ">
                            <Button
                              disabled={!isFieldsEditable}

                              onClick={() =>
                                setIsAddNewAddress({ isForPickup: false, isForDelivery: true })
                              }
                              className="w-full bg-primary-700 hover:bg-primary-800 text-white rounded-lg py-2 px-4 font-[500]"
                              aria-label="Add new location"
                              type="primary"
                              size="large"
                            >
                              + Add new location
                            </Button>
                          </Form.Item>
                        </div>
                      </div>
                    }
                  >
                    <DeliveryAddressForm
                      isAbleToEdit={isFieldsEditable}
                      form={deliveryAddressForm}
                      selectedDeliveryAddress={selectedAddresses.deliveryAddress}
                      refetch={refetch}
                      setIsFieldChanged={setIsFieldChanged}
                      isFieldChanged={isFieldChanged}
                    />
                  </Panel>
                  {isFieldChanged.deliveryAddress && activeKeys.deliveryAddress?.includes('1') && (
                    <div className="flex w-full bg-primary-25 h-[60px] font-[600] border-t border-[#e5e7eb] justify-end p-2 gap-3">
                      <Button disabled={!isFieldsEditable}
                        onClick={saveNewAddress} className="text-[#2D3484] h-[40px] ">
                        Save as new location
                      </Button>
                      <Button
                        disabled={!isFieldsEditable}
                        onClick={editExistingAddress}
                        className="bg-primary-600 text-white h-[40px] hover:!bg-primary-600 hover:!text-white"
                      >
                        Save
                      </Button>
                    </div>
                  )}
                </Collapse>

                <Collapse
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span className="text-[#2D3484] font-semibold text-[14px]">
                        {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                      </span>
                    );
                  }}
                  defaultActiveKey={['1']}
                  className="form-collapse mt-4"
                >
                  <Panel
                    key="1"
                    header={
                      <div className="flex flex-col">
                        <span
                          className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                        >
                          {t('orderEntryForms.timing.title')}
                        </span>
                      </div>
                    }
                  >
                    <TimingForm form={timingForm} isAbleToEdit={isFieldsEditable} setSelectedDateAndTime={setSelectedDateAndTime} orderSelectedTime={selectedDateAndTime} />
                  </Panel>
                </Collapse>

                <Collapse
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span className="text-[#2D3484] font-semibold text-[14px]">
                        {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                      </span>
                    );
                  }}
                  defaultActiveKey={['1']}
                  className="package-form-collapse mt-4"
                >
                  <Panel
                    key="1"
                    header={
                      <div className="flex flex-col">
                        <span
                          className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                        >
                          {t('orderEntryForms.packageDetails.title')}
                        </span>
                      </div>
                    }
                  >
                    <PackageDetailsForm
                      setPackageFormDetails={setPackageFormDetails}
                      packageList={availablePackages || []}
                      form={packageDetailsForm}
                      orderDetails={orderDetailsById}
                      isAbleToEdit={isFieldsEditable}
                    />
                  </Panel>
                </Collapse>

                <Collapse
                  expandIconPosition="end"
                  expandIcon={({ isActive }) => {
                    return (
                      <span className="text-[#2D3484] font-semibold text-[14px]">
                        {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                      </span>
                    );
                  }}
                  defaultActiveKey={['1']}
                  className="form-collapse mt-4"
                >
                  <Panel
                    key="1"
                    header={
                      <div className="flex flex-col">
                        <span
                          className={`bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb] `}
                        >
                          {t('orderEntryForms.additionalDetails.title')}
                        </span>
                      </div>
                    }
                  >
                    <AdditionalDetailsForm form={additionalDetailsForm} isAbleToEdit={isFieldsEditable} />
                  </Panel>
                </Collapse>
              </div>
            </Col>
            <Col xs={24} lg={8} className="checkout-column">
              <div className="right-column scrollable-forms-container">
                <AvailableDeliveryServices
                  availableServices={availableServices || []}
                  value={selectedDeliveryService?.id}
                  setSelectedDeliveryService={setSelectedDeliveryService}
                  getServicesStatus={getServicesByPickUpDate?.status}
                  isAbleToEdit={isFieldsEditable}
                />

                {selectedDeliveryService?.pricing?.modifiers &&
                  selectedDeliveryService.pricing.modifiers?.length > 0 && (
                    <Collapse
                      expandIconPosition="end"
                      expandIcon={({ isActive }) => {
                        return (
                          <span className="text-[#2D3484] font-semibold text-[14px]">
                            {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                          </span>
                        );
                      }}
                      defaultActiveKey={['1']}
                      className="form-collapse mt-4"
                    >
                      <Panel
                        key="1"
                        header={
                          <div className="flex flex-col">
                            <span className="bg-primary-25 h-[48px] font-[600] p-3 border-b border-[#e5e7eb]">
                              Additional services
                            </span>
                          </div>
                        }
                      >
                        <div className="flex flex-col max-h-[200px] overflow-y-auto">
                          {selectedDeliveryService.pricing.modifiers
                            .filter(
                              (modifier: IModifier) =>
                                modifier.configuration !== ModifiersConfigurationType.REQUIRED
                            )
                            .map((modifier: IModifier) => {
                              const isUnselected =
                                modifier.configuration === ModifiersConfigurationType.UNSELECTED;
                              const isSelected =
                                modifier.configuration === ModifiersConfigurationType.SELECTED;
                              return (
                                <div
                                  key={modifier.id}
                                  className="flex items-center gap-3 p-2 border rounded"
                                >
                                  <Checkbox
                                    disabled={!isFieldsEditable}
                                    id={`modifier-${modifier.id}`}
                                    checked={!!modifierSelections[modifier.id]}
                                    onChange={
                                      isSelected || isUnselected
                                        ? () => handleModifierChange(modifier.id)
                                        : undefined
                                    }
                                    tabIndex={0}
                                    aria-label={modifier.name}
                                    className="accent-primary-600"
                                  />
                                  <label
                                    htmlFor={`modifier-${modifier.id}`}
                                    className="flex-1 font-medium cursor-pointer"
                                    tabIndex={0}
                                    aria-label={modifier.name}
                                    onKeyDown={(e) => {
                                      if (
                                        (e.key === 'Enter' || e.key === ' ') &&
                                        (isSelected || isUnselected)
                                      ) {
                                        handleModifierChange(modifier.id);
                                      }
                                    }}
                                  >
                                    {modifier.name}
                                  </label>
                                  <span className="text-primary-600 font-semibold">
                                    ${modifier.amount?.toFixed(2)}
                                  </span>
                                </div>
                              );
                            })}
                        </div>
                      </Panel>
                    </Collapse>
                  )}

                <PaymentSummary
                  subTotal={subTotal}
                  additionalServicesTotal={additionalServicesTotal}
                  tax={tax}
                  total={computedTotalAmount}
                />
                <div className="flex gap-3 items-center">
                  <span>Is Stripe Required?</span>
                  <Form.Item className="mb-0">
                    <Switch checked={isStripeRequired} onChange={setIsStripeRequired} />
                  </Form.Item>
                </div>
                {isStripeRequired && (
                  <PaymentsPage
                    setIsAllFilledUp={setIsAllFilledUp}
                    isStripeRequired={isStripeRequired}
                  />
                )}

                <div className="order-button-container">
                  <Button
                    type="primary"
                    htmlType="submit"
                    block
                    className="place-order-btn"
                    disabled={!isAbleToPlaceOrder}
                  >
                    {isEditMode && orderDetailsById?.status !== 'Draft' ? 'Update Order' : t('orderEntryForms.buttons.placeOrder')}
                  </Button>
                </div>
                <div className="order-actions-container">
                  <Button
                    disabled={
                      !selectedAddresses.pickupAddress || !selectedAddresses.deliveryAddress
                    }
                    onClick={handleSaveAsDraft}
                    className="save-draft-btn"
                  >
                    {t('orderEntryForms.buttons.saveAsDraft')}
                  </Button>
                  <Button onClick={handleDiscardOrder} className="discard-order-btn">
                    {t('orderEntryForms.buttons.discardOrder')}
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </Form>
    </>
  );
};

export default OrderForm;

