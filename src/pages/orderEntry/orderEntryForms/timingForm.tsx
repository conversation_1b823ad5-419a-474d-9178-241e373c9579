import { DateCalendarIcon } from '@/assets';
import { Form, DatePicker, Row, Col } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useConfig } from '@/contexts/ConfigContext';
import { FormInstance } from 'antd/lib';

interface ITimingFormProps {
  form: FormInstance
  setSelectedDateAndTime: Dispatch<SetStateAction<string>>;
  isAbleToEdit: boolean;
  orderSelectedTime: any
}

const TimingForm: React.FC<ITimingFormProps> = (props) => {
  const { setSelectedDateAndTime, isAbleToEdit, form, orderSelectedTime } = props;
  const [currentTime, setCurrentTime] = useState('');
  const { config } = useConfig();
  useEffect(() => {
    updateCurrentTime();
    const intervalId = setInterval(updateCurrentTime, 1000);
    return () => clearInterval(intervalId);
  }, []);

  const updateCurrentTime = () => {
    const now = new Date();
    setCurrentTime(dayjs(now).format('HH:mm'));
  };
  useEffect(() => {
    if (orderSelectedTime) {
      form.setFieldsValue({
        pickupDate: dayjs(orderSelectedTime)
      });
    }
  }, [orderSelectedTime, form]);

  return (
    <Form form={form} layout="vertical" requiredMark={false} className="timing-form">
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            className="timing-form-item"
            label="Pickup time"
            name="pickupDate"
            rules={[{ required: true, message: 'Pickup date is required' }]}
          >
            <DatePicker
              value={orderSelectedTime ? dayjs(orderSelectedTime) : undefined}
              disabled={!isAbleToEdit}
              suffixIcon={<DateCalendarIcon />}
              showTime
              format={config.dateFormate}
              disabledDate={(currentDate) => {
                return currentDate && currentDate < dayjs().startOf('day');
              }}
              disabledTime={(current) => {
                const now = dayjs();
                if (current.isSame(now, 'day')) {
                  return {
                    disabledHours: () => {
                      const hours = [];
                      for (let i = 0; i < now.hour() + 1; i++) {
                        hours.push(i);
                      }
                      return hours;
                    },
                  };
                }
                return {};
              }}
              placeholder={'Select date and time'}
              className="timing-form-item w-full h-[40px]"
              onOk={(time) => {
                setSelectedDateAndTime(time.utc().toISOString());
              }}
              onChange={(date) => {
                if (date) {
                  setSelectedDateAndTime(date.utc().toISOString());
                }
              }}
              dropdownClassName="orders-general-datepicker-dropdown"
            />
          </Form.Item>
        </Col>

        <div className="p-8 flex gap-3">
          <span className="font-[500] text-[16px] text-primary-900">Current date & time:</span>
          <div className="flex gap-2">
            <span className="font-[600] text-[18px] text-primary-900 font-inter">
              {dayjs().format('MMMM DD, YYYY')}
            </span>
            <span className="font-[600] text-[18px] text-primary-900">{currentTime}</span>
          </div>
        </div>
      </Row>
    </Form>
  );
};

export default TimingForm;
