import { ordersServiceHook } from '@/api/order/useOrders';
import { PhoneOutlinedIcon } from '@/assets';
import { CustomCollapse } from '@/components/common/customCollaps/CustomCollapse';
import NotFound404 from '@/components/common/statusFallbackPage/NotFound404';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import { getOrderStatusClassName } from '@/lib/helper';
import { dateFormatter } from '@/lib/helper/dateHelper';
import Icon, { LoadingOutlined } from '@ant-design/icons';
import { Image, Spin } from 'antd';
import { useParams } from 'react-router-dom';
import OrderStatusSteps from '../orderDetails/OrderStatusHistory';

const OrderTracking = () => {
  const { id: trackingNumber } = useParams();
  const { t } = useLanguage();
  const { config } = useConfig();

  const {
    data: trackingDetails,
    error,
    isFetching,
  } = ordersServiceHook.useEntity(`customer/tracking/${trackingNumber}` as string, {
    enabled: Boolean(trackingNumber),
  });

  const orderTrackingDetailsObject = {
    dataArray: [
      {
        key: `totalWeight`,
        label: `${t('ordersPage.orderInfoDataFields.weight')}(${config.units?.weight})`,
        value: trackingDetails?.totalWeight || 'N/A',
      },
      {
        key: 'description',
        label: 'Description',
        value: trackingDetails?.description || 'N/A',
      },
      {
        key: 'collectionFrom',
        label: 'Collection from',
        value: trackingDetails?.collectionContactName || 'N/A',
      },
      {
        key: 'deliveryTo',
        label: 'Delivery to',
        value: trackingDetails?.deliveryContactName || 'N/A',
      },
      {
        key: 'CollectionSignature',
        label: 'Collection signature',
        value: trackingDetails?.attachments?.pickupSignature ? (
          <div className="border border-primary-100 rounded-lg w-fit p-2">
            <Image
              src={trackingDetails?.attachments?.pickupSignature?.url}
              width={72}
              height={72}
              loading="lazy"
            />
          </div>
        ) : (
          'N/A'
        ),
      },
      {
        key: 'DeliverySignature',
        label: 'Delivery signature',
        value: trackingDetails?.attachments?.pickupSignature ? (
          <div className="border border-primary-100 rounded-lg w-fit p-2">
            <Image
              src={trackingDetails?.attachments?.deliverySignature?.url}
              width={72}
              height={72}
              loading="lazy"
            />
          </div>
        ) : (
          'N/A'
        ),
      },
    ],
    images: true,
  };

  const collapseDetailsRendered = (obj: any): React.ReactNode => {
    return (
      <>
        <div className="grid lg:grid-cols-2 w-full gap-2">
          {obj?.dataArray?.map((item: { label: string; value: string }, index: number) => {
            return (
              <div className="flex w-full p-1">
                <div key={index} className=" flex items-center lg:w-[25%] w-full">
                  <span className="w-48 flex items-center gap-1 font-medium text-primary-900">
                    {item.label}:
                  </span>
                </div>
                <div className="lg:w-[75%] w-full text-primary-900 font-medium">{item.value}</div>
              </div>
            );
          })}
        </div>
        {obj.images && (
          <>
            <div className="mt-4">
              <h2 className="font-semibold text-primary-900 text-base">
                {t('ordersPage.orderDetailsPage.uploadedImages')}
              </h2>
              <div className="mt-4 flex gap-2">
                {trackingDetails?.attachments?.images?.length === 0
                  ? 'N/A'
                  : trackingDetails?.attachments?.images?.map((img) => (
                      <div className="border border-primary-100 rounded-lg w-fit p-2">
                        <Image src={img?.url} alt="" width={72} height={72} loading="lazy" />
                      </div>
                    ))}
              </div>
            </div>
          </>
        )}
      </>
    );
  };

  const itemsForOrderDetails = [
    {
      key: 'orderDetails',
      label: 'Order details',
      children: collapseDetailsRendered(orderTrackingDetailsObject) as React.ReactNode,
    },
  ];

  return (
    <>
      {error ? (
        <NotFound404
          title="Order Not Found"
          description="The order you are looking for does not exist."
        />
      ) : (
        <div className=" flex flex-col gap-4 px-4 py-4 text-primary-900">
          <div className="flex justify-between items-center">
            <div className="text-lg font-semibold md:text-3xl">
              Tracking number: {trackingDetails?.trackingNumber || 'N/A'}
            </div>
            <div className="flex bg-primary-25 border border-primary-50 p-3 rounded-lg">
              <div className="flex gap-1 flex-col border-r border-r-primary-100 pr-4 ">
                <span className="font-semibold">Status</span>
                <span
                  className={`${getOrderStatusClassName(trackingDetails?.status as string)} text-sm`}
                >
                  {trackingDetails?.status || 'N/A'}
                </span>
              </div>
              <div className="flex gap-1 flex-col pl-4">
                <span className="font-semibold">Estimated time</span>
                {trackingDetails?.scheduledDeliveryTime
                  ? dateFormatter(trackingDetails?.scheduledDeliveryTime as unknown as string)
                  : 'N/A'}
              </div>
            </div>
          </div>

          <OrderStatusSteps orderDetails={trackingDetails as any} />

          <CustomCollapse
            defaultActiveKey={['orderDetails']}
            items={itemsForOrderDetails}
            collapseProps={{ collapsible: 'icon' }}
            collapsePanelProps={{
              showArrow: false,
              key: 'orderDetails',
              header: 'Order details',

              extra: isFetching ? (
                <Spin
                  indicator={
                    <div className="text-primary-600 !h-fit !w-fit">
                      <LoadingOutlined spin className="text-primary-600 block" />
                    </div>
                  }
                  size="small"
                />
              ) : (
                ''
              ),
            }}
          />
          <div className="flex justify-center gap-2">
            Customer support:
            <span className=" font-semibold">
              <Icon component={PhoneOutlinedIcon} className="mr-2" />
              (*************
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default OrderTracking;
