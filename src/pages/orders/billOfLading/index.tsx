import { Lu<PERSON><PERSON><PERSON><PERSON> } from '@/assets';
import Barcode from 'react-barcode';

const BillOfLadingPDFView = () => {
  return (
    <div className="max-w-4xl mx-auto bg-white p-6 text-sm text-primary-900 font-sans shadow-lg">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <img src={LumigoLogo} alt="Lumigo" className="h-8 mb-2" />
          <p className="text-xs">
            Lumigo Solution 2015 Inc (Lumigo Transport) <br />
            Phone: ************ Website: www.lumigotransport.ca
          </p>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-semibold">Bill of lading</h2>
          <p className="text-xs">Submitted on: 11/08/2025, 02:00 PM</p>
        </div>
      </div>

      {/* Tracking Number */}
      <div className="bg-primary-50 border text-base border-primary-900 py-2 px-4 mb-4 text-center font-bold">
        Tracking number: #147795
      </div>

      {/* Sender/Receiver */}
      <div className="grid grid-cols-2 border border-primary-900 mb-4">
        <div className="border-r border-r-primary-900">
          <div className="px-2 pt-2 pb-4 flex gap-2">
            <p className="font-semibold">Sender:</p>
            <p>
              EliteEdge Press,
              <br />
              7520 Chemin de la cote-de-Liesse
              <br />
              Saint-Laurent H4T 1E7
            </p>
          </div>
        </div>
        <div className="p-2 flex gap-2">
          <p className="font-semibold">Receiver:</p>
          <p>
            MAJESTIX, NOOR,
            <br />
            340 Rue Aime-Vincent 514-647-4350
            <br />
            Vaudreuil-Dorion J7V 5V5
          </p>
        </div>
      </div>

      {/* Barcode Section */}
      <div className="flex items-center gap-4 border border-primary-900 p-3 mb-4 flex-wrap">
        <div className="w-[40%]">
          <div className="flex flex-col justify-start items-center w-fit">
            <Barcode value="1245 4588 4545 10" className="w-[200px] h-[70px]" fontSize={30} />
          </div>
        </div>
        <div className="text-xs text-right flex gap-2 flex-col flex-1">
          <p className="flex flex-col items-start text-sm">
            <span className="font-semibold !text-sm">Service Level:</span> Sameday
          </p>
          <div className="flex gap-4">
            <p className="text-sm">
              <span className="font-semibold !text-sm">Collect at:</span> 12/08/2025, 10:00 AM
            </p>
            <p className="text-sm">
              <span className="font-semibold !text-sm">Delivery at:</span> 12/08/2025, 10:00 AM
            </p>
          </div>
        </div>
      </div>

      {/* Shipment Info Table */}
      <div className="border border-primary-900 divide-y divide-primary-900 mb-4">
        {[
          ['Description:', '1 Package + 1 Box = Total: 2'],
          ['PO Number:', ''],
          ['Quantity:', '2'],
          ['Weight:', '1'],
          ['Dimensions:', '0L x 0W x 0H'],
          ['Department:', ''],
          ['Ref. Number:', ''],
          ['Declared Number:', '$1500.00'],
          [
            'Shipper:',
            'EliteEdge Press, 7520 Chemin de liesse Saint Laurent, Quebec H4T 1E7 Canada',
          ],
        ].map(([label, value], index) => (
          <div key={index} className="flex">
            <div className="w-[20%] p-2 font-bold border-r border-r-primary-900">{label}</div>
            <div className="w-[80%] p-2">{value}</div>
          </div>
        ))}
      </div>

      {/* Signature Section */}
      <div className="grid grid-cols-2 gap-6 text-xs">
        {['Collection', 'Delivery'].map((section, i) => (
          <div key={i} className="">
            <div className="bg-primary-50 px-2 py-2 w-fit font-bold border border-primary-900 text-sm">
              {section}
            </div>
            <div className="flex flex-col gap-6 p-2 pt-4 space-y-2">
              {['Driver:', 'Vehicle:', 'Date & time:', 'Received from:'].map((field, j) => (
                <div key={j} className="flex gap-1 text-sm">
                  <p className="font-bold min-w-max">{field}</p>
                  <div className="border-b border-primary-900 w-full h-4 px-2 pb-[18px]"></div>
                </div>
              ))}
              <div className="flex gap-1 !mt-6">
                <p className="font-bold min-w-max text-sm">Signature:</p>
                <div className="border-b border-primary-900 w-full h-4 px-2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BillOfLadingPDFView;
