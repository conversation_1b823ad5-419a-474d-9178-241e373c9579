import { LumigoLogo } from '@/assets';

const OrderTrackingEmail = () => {
  return (
    <table className="w-full bg-[#f4f4f4] py-5" cellPadding="0" cellSpacing="0" role="presentation">
      <tr>
        <td align="center">
          <table
            className="w-full max-w-[600px] bg-white m-auto rounded-md overflow-hidden font-sans"
            cellPadding="0"
            cellSpacing="0"
            role="presentation"
          >
            {/* Header */}
            <tr>
              <td className="p-5">
                <img src={LumigoLogo} alt="Lumigg Logo" />
              </td>
            </tr>

            {/* Body */}
            <tr>
              <td className="p-5 text-[#333] text-sm">
                <h2 className="text-lg font-semibold mb-2">New Order Placed – #5242545</h2>
                <p className="mb-2">Dear Sam,</p>
                <p className="mb-4">
                  A new order has been successfully placed with the following details:
                </p>

                <table
                  className="bg-[#f9f9f9] border border-[#eee] rounded w-full text-sm mb-4"
                  cellPadding="5"
                >
                  <tr>
                    <td className="font-bold">Order Number:</td>
                    <td>#5242545</td>
                  </tr>
                  <tr>
                    <td className="font-bold">Service Chosen:</td>
                    <td>Same day</td>
                  </tr>
                </table>

                <p className="mb-4">You can track the order's progress using the following link:</p>
                <a
                  href="#"
                  className="inline-block px-5 py-2 bg-[#3b3dbd] text-white rounded-md text-sm font-medium no-underline mb-4"
                >
                  Track Order
                </a>

                <p className="mt-4 mb-2">
                  If you have any questions or need further assistance, please don’t hesitate to
                  contact us at{' '}
                  <a href="mailto:<EMAIL>" className="text-blue-600 underline">
                    <EMAIL>
                  </a>{' '}
                  or +251 125 1245 2154.
                </p>

                <p className="mb-4">Thank you for choosing Lumigo Transport!</p>

                <p className="mb-2">
                  Best Regards,
                  <br />
                  <span className="font-bold">Team Lumigo</span>
                </p>

                <p className="text-[#999] text-xs mt-2">
                  This is a system generated message. Do not reply.
                </p>
              </td>
            </tr>

            {/* Footer */}
            <tr>
              <td className="p-5 text-center text-xs text-[#999]">
                <p className="mb-3">
                  <a href="#" className="inline-block mx-1">
                    <img src="https://via.placeholder.com/20x20?text=T" alt="Twitter" />
                  </a>
                  <a href="#" className="inline-block mx-1">
                    <img src="https://via.placeholder.com/20x20?text=F" alt="Facebook" />
                  </a>
                  <a href="#" className="inline-block mx-1">
                    <img src="https://via.placeholder.com/20x20?text=I" alt="LinkedIn" />
                  </a>
                </p>
                <p className="mb-2">
                  <img src={LumigoLogo} alt="Lumigg Logo" className="mx-auto" />
                </p>
                <p className="mb-1">
                  +1 (234) 567-8901 |{' '}
                  <a href="mailto:<EMAIL>" className="text-blue-600">
                    <EMAIL>
                  </a>
                </p>
                <p className="mt-2">
                  Copyright © 2020 Lumigo.
                  <br />A better company begins with a personalised shipment experience.
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
};

export default OrderTrackingEmail;
