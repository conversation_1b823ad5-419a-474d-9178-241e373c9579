import CustomDivider from '@/components/common/divider/CustomDivider';
import { useLanguage } from '@/hooks/useLanguage';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Form, Input, InputNumber, InputRef, Select, Space, Switch } from 'antd';
import MaskedInput from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import TextArea from 'antd/es/input/TextArea';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { formErrorRegex } from '@/constant/Regex';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { infoCircleOutlined } from '@/assets';
import { Autocomplete } from '@react-google-maps/api';
import { useGooglePlaceDropdownFix } from '@/hooks/useGooglePlaceDropdownFix';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { IAddressOperationFormProps } from './addressOperation.types';
import { zoneService } from '@/api/zones/useZones';
import { AxiosError } from 'axios';
import { TrackedError } from '@/types/AxiosTypes';
import { RuleObject } from 'antd/es/form';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';

const AddressOperationForm: React.FC<IAddressOperationFormProps> = (props) => {
  const { form, onFinish, open, cellData } = props;

  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const inputPhoneRef = useRef<InputRef>(null);

  const { t } = useLanguage();

  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const autocompleteRef = useRef<Autocomplete>(null);

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);

  const PhoneNumberAddonBefore = (
    <Form.Item name="countryCode" noStyle initialValue={'USA +1'}>
      <Select
        options={optionsForPrefix}
        placeholder={t('common.usa')}
        onChange={(value) => maskingInputPhone(value)}
      ></Select>
    </Form.Item>
  );

  useGooglePlaceDropdownFix(open.isOpen, 'autoComplete');

  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const onPlaceChanged = useCallback(async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);

      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
        latitude: selectedPlaceData?.latitude,
        longitude: selectedPlaceData?.longitude,
      };
      form.setFieldsValue(newFormValues);
      form.validateFields(['postalCode']);
    }
  }, [form, searchResult]);

  useEffect(() => {
    if (open.isEdit) {
      const phoneNumberFormat = cellData?.phoneNumber?.toString();
      form.setFieldsValue({
        ...cellData,
        countryCode: cellData.countryCode,
        phoneNumber: phoneNumberFormat,
      });
      maskingInputPhone(cellData.countryCode);
    }
  }, [open.isEdit, cellData, form, maskingInputPhone]);

  const onAutocompleteChangeHandler = () => {
    const val = form.getFieldsValue(['province', 'city', 'country', 'postalCode']);
    if (val.province || val.city || val.country || val.postalCode) {
      form.resetFields(['province', 'city', 'country', 'postalCode']);
    }
  };

  const addressZoneValidator = useCallback(
    async (_: RuleObject, value: string) => {
      try {
        const trimmedPostalCode = value.split(' ')[0];
        await zoneService.getById(`${trimmedPostalCode}`);
        return Promise.resolve();
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === '406007') {
            return Promise.reject(
              new Error(
                t('addressPage.operationalForm.zoneError', {
                  code: errorStack?.details?.postalCode.split(' ')[0],
                })
              )
            );
          }
        }
        return Promise.resolve();
      }
    },
    [t]
  );

  return (
    <Form
      scrollToFirstError={{ behavior: 'smooth' }}
      name="address-form"
      layout="vertical"
      className="custom-form"
      form={form}
      onFinish={onFinish}
      preserve={false}
    >
      <CustomDivider label={t('common.divider.basicDetails')} />
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.name')}
            validateFirst
            name="name"
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.nameError') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                pattern: formErrorRegex.NoSpecialCharacters,
                message: t('common.errors.noSpacialCharacters'),
              },
              {
                whitespace: true,
                message: t('customerAddressPage.operationalForm.nameError'),
              },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.namePlaceholder')}
              maxLength={50}
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.companyName')}
            name="companyName"
            validateFirst
            rules={[
              {
                required: true,
                message: t('customerAddressPage.operationalForm.companyNameError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                whitespace: true,
                message: t('customerAddressPage.operationalForm.companyNameError'),
              },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.companyNamePlaceholder')}
              maxLength={50}
            />
          </Form.Item>
        </div>

        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.email')}
            name="email"
            validateFirst
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.emailError') },
              { type: 'email', message: t('customerAddressPage.operationalForm.emailTypeError') },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.emailPlaceholder')}
              maxLength={80}
            />
          </Form.Item>
          <Space.Compact className="combined-input">
            <Form.Item
              className="w-[75%]"
              dependencies={['countryCode']}
              validateFirst
              rules={[
                {
                  required: true,
                  validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                },
                {
                  validator: (_, value) =>
                    validateMaskedInput(
                      value,
                      maskPhoneInput.length,
                      t('customerAddressPage.operationalForm.validPhoneNumberError')
                    ),
                },
              ]}
              name="phoneNumber"
              label={t('customerAddressPage.operationalForm.phoneNumber')}
            >
              <MaskedInput
                ref={inputPhoneRef}
                addonBefore={PhoneNumberAddonBefore}
                className="customer-general-maskedInput address-popup-maskedInput"
                placeholder={t('customerAddressPage.operationalForm.phoneNumberPlaceholder')}
                mask={maskPhoneInput.mask}
                onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
              />
            </Form.Item>
            <Form.Item name="phoneExtension" className="w-[25%]">
              <Input
                placeholder="00000"
                maxLength={6}
                onKeyDown={(e) => numberFieldValidator(e, {})}
              />
            </Form.Item>
          </Space.Compact>
        </div>
        <CustomDivider
          label={t('customerAddressPage.operationalForm.locationDividerText')}
          className="py-2"
        />
        <CustomGoogleAutoComplete
          onLoad={onLoad}
          onPlaceChanged={onPlaceChanged}
          ref={autocompleteRef}
        >
          <Form.Item
            validateFirst
            label={t('customerAddressPage.operationalForm.addressLine1')}
            rules={[
              {
                required: true,
                message: t('customerAddressPage.operationalForm.addressLine1Error'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                whitespace: true,
                message: t('customerAddressPage.operationalForm.addressLine1Error'),
              },
            ]}
            name="addressLine1"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.addressLine1Placeholder')}
              maxLength={255}
              id="autoComplete"
              onChange={onAutocompleteChangeHandler}
            />
          </Form.Item>
        </CustomGoogleAutoComplete>
        <Form.Item name="latitude" hidden preserve>
          <InputNumber />
        </Form.Item>
        <Form.Item name="longitude" hidden preserve>
          <InputNumber />
        </Form.Item>
        <Form.Item
          label={t('customerAddressPage.operationalForm.addressLine2')}
          name="addressLine2"
          validateFirst
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              whitespace: true,
              message: t('customerAddressPage.operationalForm.validInput'),
            },
          ]}
        >
          <Input
            placeholder={t('customerAddressPage.operationalForm.addressLine2Placeholder')}
            maxLength={255}
          />
        </Form.Item>
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.city')}
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.cityError') },
            ]}
            name="city"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.cityPlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>

          <Form.Item
            label={t('customerAddressPage.operationalForm.province')}
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.provinceError') },
            ]}
            name="province"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.provincePlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.postalCode')}
            validateFirst
            validateDebounce={2000}
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.postalCodeError') },
              {
                pattern: formErrorRegex.PostalCode,
                message: t('customerAddressPage.operationalForm.validPostalCodeError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                validator: addressZoneValidator,
              },
            ]}
            name="postalCode"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.postalCodePlaceholder')}
              maxLength={20}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.country')}
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.countryError') },
            ]}
            name="country"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.countryPlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
        </div>

        <Form.Item
          label={
            <span className="flex gap-1">
              {t('customerAddressPage.operationalForm.comments')}
              <CustomTooltip title={t('customerAddressPage.operationalForm.notesTooltip')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
          validateFirst
          name="notes"
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              whitespace: true,
              message: t('customerAddressPage.operationalForm.validInput'),
            },
          ]}
        >
          <TextArea
            placeholder={t('customerAddressPage.operationalForm.commentsPlaceHolder')}
            maxLength={500}
            style={{
              minHeight: 54,
              maxHeight: 100,
            }}
          />
        </Form.Item>
        <CustomDivider label={t('customerAddressPage.operationalForm.favoriteSection')} />
        <div className="flex gap-4 flex-col md:flex-row">
          <div className="flex gap-3 flex-col flex-1">
            <h3 className="text-[#090A1A] font-semibold text-base">
              {t('customerAddressPage.operationalForm.addToFavorite')}
            </h3>
            <div className="flex gap-4 justify-between">
              <Form.Item
                label={
                  <>
                    <CustomTooltip
                      title={t('customerAddressPage.operationalForm.favoritePickupTooltip')}
                    >
                      <img src={infoCircleOutlined} />
                    </CustomTooltip>
                    {t('customerAddressPage.operationalForm.pickup')}
                  </>
                }
                name="isFavoriteForPickup"
                layout="horizontal"
                className="switch-form-item"
              >
                <Switch size="small" />
              </Form.Item>
              <Form.Item
                label={
                  <>
                    <CustomTooltip
                      title={t('customerAddressPage.operationalForm.favoriteDeliveryTooltip')}
                    >
                      <img src={infoCircleOutlined} />
                    </CustomTooltip>
                    {t('customerAddressPage.operationalForm.delivery')}
                  </>
                }
                name="isFavoriteForDelivery"
                layout="horizontal"
                className="switch-form-item"
              >
                <Switch size="small" />
              </Form.Item>
            </div>
          </div>
          <div className="flex gap-3 flex-col flex-1">
            <h3 className="text-[#090A1A] font-semibold text-base">
              {t('customerAddressPage.operationalForm.addToDefault')}
            </h3>
            <div className="flex gap-4">
              <Form.Item
                label={
                  <>
                    <CustomTooltip
                      title={t('customerAddressPage.operationalForm.defaultPickupTooltip')}
                    >
                      <img src={infoCircleOutlined} />
                    </CustomTooltip>
                    {t('customerAddressPage.operationalForm.pickup')}
                  </>
                }
                name="isDefaultForPickup"
                layout="horizontal"
                className="switch-form-item"
              >
                <Switch size="small" />
              </Form.Item>
            </div>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default memo(AddressOperationForm);
