import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { useLanguage } from '@/hooks/useLanguage';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ICellRendererParams, IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { getPaginationData } from '@/lib/helper';
import ColumnManage from '@/components/specific/columnManage';
import Icon from '@ant-design/icons';
import {
  DeleteIcon,
  deleteSvg,
  DuplicateCustomerIcon,
  EyeIcon,
  HistoryIcon,
  PlusButtonIcon,
} from '@/assets';
import { But<PERSON>, Divider, Form } from 'antd';
import AddLocationIcon from '@/assets/icons/addLocationIcon';
import CustomModal from '@/components/common/modal/CustomModal';
import { IIsOpenModal } from '@/types/CommonTypes';
import CustomerAddressOperation from './addressOperation/index';
import notificationManagerInstance, { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { addressServiceHook } from '@/api/address/useAddress';
import { defaultPagination } from '@/constant/generalConstant';
import { AddressPreferencesDTO, GetAddressDto } from '@/api/address/address.types';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { IAssignedFilters } from '@/components/specific/activeFilters/activeFiltersTypes';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { FavoriteAddressIconOutlined } from '@/assets/icons/favouriteAddressIconOutlined';
import useThrottle from '@/hooks/useThrottle';

const AddressPage = () => {
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const { t } = useLanguage();
  const [isModalOpen, setIsModalOpen] = useState<IIsOpenModal>({ isOpen: false, isEdit: false });
  const {
    data: customerAddress,
    refetch,
    isLoading,
    isFetching,
  } = addressServiceHook.useList(filterParams, { staleTime: 30000 });

  const [addresses, setAddresses] = useState<GetAddressDto[]>();
  const [cellData, setCellData] = useState<GetAddressDto>({} as GetAddressDto);
  const notificationManager = useNotificationManager();
  const gridRef = useRef<AgGridReact<GetAddressDto>>(null);
  const [customerAddressForm] = Form.useForm();

  useEffect(() => {
    if (customerAddress) {
      setAddresses(customerAddress.data);
    }
  }, [customerAddress]);

  const handleDeleteCustomerAddress = addressServiceHook.useDelete({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successDelete'),
      });
      customAlert.destroy();
      refetch();
    },
  });

  const duplicateAddressHandler = addressServiceHook.useDuplicate({
    onSuccess: async () => {
      refetch();
      customAlert.destroy();
    },
  });

  const duplicateAddressConfirmation = useCallback(
    (duplicateAddressPayload: GetAddressDto) =>
      customAlert.warning({
        title: t('customerAddressPage.notifications.confirmDuplicate'),
        message: t('customerAddressPage.notifications.confirmDuplicateMessage'),
        secondButtonTitle: t('common.cancel'),
        firstButtonTitle: t('common.duplicate'),
        firstButtonFunction: async () => {
          await duplicateAddressHandler.mutate(duplicateAddressPayload.id);
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      }),
    [duplicateAddressHandler, t]
  );

  const deleteAddressConfirmation = useCallback(
    (id: string) =>
      customAlert.error({
        title: t('customerAddressPage.notifications.confirmDelete'),
        message: t('customerAddressPage.notifications.confirmDeleteMessage'),
        secondButtonTitle: t('common.cancel'),
        firstButtonTitle: t('common.delete'),
        firstButtonFunction: async () => {
          await handleDeleteCustomerAddress.mutateAsync(id);
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      }),
    [handleDeleteCustomerAddress, t]
  );

  const paginationData = useMemo(() => getPaginationData(customerAddress), [customerAddress]);
  const createCustomerAddressMutation = addressServiceHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successCreate'),
      });
      refetch();
    },
  });
  const updateCustomerAddressMutation = addressServiceHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successUpdate'),
      });
      refetch();
    },
  });

  const updateAddressPreferencesMutation = addressServiceHook.useUpdate<AddressPreferencesDTO>({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('addressPage.notification.successPreferencesUpdate'),
      });
    },
  });

  const customerAddressContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('addressPage.contextMenu.newAddress'),
        key: 'newCustomerAddress',
        icon: AddLocationIcon as React.ElementType,
        onClick: () => setIsModalOpen({ isOpen: true, isEdit: false }),
      },
      {
        label: t('addressPage.contextMenu.duplicateAddress'),
        icon: DuplicateCustomerIcon as React.ElementType,
        key: 'duplicateCustomerAddress',
        onClick: () => duplicateAddressConfirmation(cellData),
      },
      {
        label: t('addressPage.contextMenu.favAddress'),
        icon: FavoriteAddressIconOutlined as React.ElementType,
        key: 'markAsFavorites',
        subMenu: [
          {
            label: t('customerAddressPage.operationalForm.pickup'),
            key: 'markAsFavoritesForPickup',
            onClick: async () => {
              await updateAddressPreferencesMutation.mutateAsync({
                id: `${cellData.id}/preferences`,
                data: {
                  isFavoriteForDelivery: cellData.isFavoriteForDelivery,
                  isFavoriteForPickup: true,
                  isDefaultForPickup: cellData.isDefaultForPickup,
                },
              });
              await refetch();
            },
          },
          {
            label: t('customerAddressPage.operationalForm.delivery'),
            key: 'markAsFavoritesForDelivery',
            onClick: async () => {
              await updateAddressPreferencesMutation.mutateAsync({
                id: `${cellData.id}/preferences`,
                data: {
                  isFavoriteForDelivery: true,
                  isFavoriteForPickup: cellData.isFavoriteForPickup,
                  isDefaultForPickup: cellData.isDefaultForPickup,
                },
              }),
                await refetch();
            },
          },
        ],
        onClick: () => {},
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'deleteCustomerAddress',
        onClick: () => deleteAddressConfirmation(cellData.id as string),
      },
    ];
  }, [
    cellData,
    deleteAddressConfirmation,
    duplicateAddressConfirmation,
    refetch,
    t,
    updateAddressPreferencesMutation,
  ]);

  const viewCustomerAddress = useCallback(
    (params: ICellRendererParams<GetAddressDto>) => {
      setIsModalOpen({ isOpen: true, isEdit: true });
      customerAddressForm.setFieldsValue(params.data);
      setCellData(params.data);
    },
    [customerAddressForm]
  );

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.address.sortable.includes(field);
  }, []);

  const addressColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'companyName',
        headerName: t('dashboard.customer.columns.companyName'),
        sortable: isColumnSortable('companyName'),
        unSortIcon: isColumnSortable('companyName'),
        tooltipField: 'companyName',
        minWidth: 200,
        flex: 1,
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'name',
        headerName: t('customerAddressPage.colDefs.name'),
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'addressLine1',
        headerName: t('dashboard.customer.columns.addressLine1'),
        sortable: isColumnSortable('addressLine1'),
        unSortIcon: isColumnSortable('addressLine1'),
        visible: true,
        type: 'string',
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'addressLine2',
        headerName: t('customerAddressPage.colDefs.addressLine2'),
        sortable: isColumnSortable('addressLine2'),
        unSortIcon: isColumnSortable('addressLine2'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'city',
        type: 'string',
        headerName: t('customerAddressPage.colDefs.city'),
        minWidth: 200,
        flex: 1,
        sortable: isColumnSortable('city'),
        unSortIcon: isColumnSortable('city'),
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'postalCode',
        headerName: t('addressPage.colDefs.postalCode'),
        sortable: isColumnSortable('postalCode'),
        unSortIcon: isColumnSortable('postalCode'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'phoneNumber',
        headerName: t('customerAddressPage.colDefs.phone'),
        sortable: isColumnSortable('phoneNumber'),
        unSortIcon: isColumnSortable('phoneNumber'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'email',
        headerName: t('customerAddressPage.colDefs.email'),
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'isFavoriteForPickup',
        headerName: t('customerAddressPage.colDefs.isFavoriteForPickup'),
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          const value = params.value
            ? t('customerAddressPage.common.yes')
            : t('customerAddressPage.common.no');
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'isFavoriteForDelivery',
        headerName: t('customerAddressPage.colDefs.isFavoriteForDelivery'),
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          const value = params.value
            ? t('customerAddressPage.common.yes')
            : t('customerAddressPage.common.no');
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'action',
        headerName: t('customerAddressPage.colDefs.action'),
        pinned: 'right',
        width: 110,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<GetAddressDto>) => {
          return (
            <div className="flex gap-2 h-full items-center w-full overflow-hidden">
              <CustomTooltip
                content={
                  <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                    {t('common.added')}{' '}
                    <span className=" block w-fit text-sm font-semibold">
                      {' '}
                      {dateFormatter(params.data.createdAt)}{' '}
                    </span>
                    <hr className="border-[#0000001c]" />
                    {t('common.modified')}:{' '}
                    <span className=" block w-fit text-sm font-semibold">
                      {dateFormatter(params.data.updatedAt)}
                    </span>
                    <hr className="border-[#0000001c]" />
                    {t('common.lastUpdatedBy')}:{' '}
                    <span className="block w-fit text-sm font-semibold">
                      {params.data.updatedBy || t('common.notUpdatedYet')}
                    </span>
                  </div>
                }
                placement="leftTop"
              >
                <div>
                  <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
                </div>
              </CustomTooltip>
              <Icon
                component={() => <EyeIcon bool={false} />}
                onClick={() => {
                  viewCustomerAddress(params);
                }}
                className="cursor-pointer"
              />
              <Icon
                onClick={() => deleteAddressConfirmation(params.data.id as string)}
                component={DeleteIcon}
                className="cursor-pointer"
              />
            </div>
          );
        },
        visible: true,
      },
    ];
  }, [deleteAddressConfirmation, isColumnSortable, searchText, t, viewCustomerAddress]);

  const closeModalHandler = useCallback(() => {
    setIsModalOpen({ isOpen: false, isEdit: false });
  }, []);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="rounded-lg border-[#96A9B1]" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="address-form"
          htmlType="submit"
          type="primary"
          loading={isFetching || isLoading}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {isModalOpen.isEdit ? t('common.update') : t('common.save')}
        </Button>
      </footer>
    ),
    [closeModalHandler, isFetching, isLoading, isModalOpen.isEdit, t]
  );

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.customerPortalAddressGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const onFinishHandler = useThrottle(async (values: GetAddressDto) => {
    try {
      if (!values?.latitude || !values?.longitude) {
        notificationManagerInstance.error({
          message: t('common.error'),
          description: t('addressPage.operationalForm.noCoordinatesFound'),
        });
        return;
      }
      if (isModalOpen.isEdit) {
        await updateCustomerAddressMutation.mutateAsync({
          id: cellData.id as string,
          data: {
            ...values,
            countryCode: values?.countryCode,
            phoneNumber: values?.phoneNumber,
            phoneExtension: values?.phoneExtension?.toString(),
          },
        });
      } else {
        await createCustomerAddressMutation.mutateAsync({
          ...values,
          countryCode: values.countryCode,
          phoneNumber: values.phoneNumber,
        });
      }
      await refetch();
      setIsModalOpen({ isOpen: false, isEdit: false });
    } catch (error) {
      notificationManager.error({
        message: t('common.error'),
        description: t('common.somethingWrong'),
      });
    }
  }, 3000);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  return (
    <>
      <CustomModal
        modalTitle={
          isModalOpen.isEdit
            ? t('customerAddressPage.modal.editAddress')
            : t('customerAddressPage.modal.addAddress')
        }
        modalDescription={
          isModalOpen.isEdit
            ? t('customerAddressPage.modal.EditAddressDescription')
            : t('customerAddressPage.modal.AddAddressDescription')
        }
        open={isModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        destroyOnClose
        keyboard={false}
        maskClosable={false}
      >
        <CustomerAddressOperation
          form={customerAddressForm}
          onFinish={onFinishHandler}
          open={isModalOpen}
          cellData={cellData}
        />
      </CustomModal>
      <div className={`h-[93%] px-5`}>
        <header className="flex justify-between items-center gap-3">
          <div className=" text-[#090A1A] font-semibold text-3xl self-end">
            {t('customerAddressPage.pageTitle')}
          </div>
          <div className="flex justify-end items-center gap-3">
            <SearchFilterComponent
              colDefs={addressColDefs}
              isSetQuickFilter={false}
              searchInputPlaceholder={t('customerAddressPage.searchPlaceholder')}
              onSearch={searchHandler}
              onFilterApply={applyFilters}
              setSelectedQuickFilterData={setSelectedQuickFilterData}
              supportedFields={filterableModules.address.advanceFilter}
              clearAllFunctionRef={clearAllFunctionRef}
              setFilterParams={setFilterParams}
            />
            <ColumnManage colDefs={addressColDefs} gridName={GridNames.customerPortalAddressGrid} />
            <div className="pt-5">
              <Divider type="vertical" className="h-[40px] !m-0" />
            </div>
            <Button
              onClick={() => setIsModalOpen({ isOpen: true, isEdit: false })}
              icon={<PlusButtonIcon />}
              className="mt-5 h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            >
              {t('addressPage.modal.addAddress')}
            </Button>
          </div>
        </header>
        <main
          className={`${selectedQuickFilterData.length > 0 ? 'overflow-y-hidden h-[95%]' : 'h-full'} overflow-x-hidden overflow-y-auto bg-white`}
        >
          <ActiveFilters
            selectedQuickFilterData={selectedQuickFilterData}
            clearAllToDefault={clearAllToDefault}
            colDefs={addressColDefs}
            className={'pt-5'}
          />
          <div className="mx-auto h-full flex justify-center items-center pt-5">
            <CustomAgGrid
              rowData={addresses}
              gridRef={gridRef}
              columnDefs={addressColDefs}
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
              loading={isFetching || isLoading}
              isContextMenu
              contextMenuItem={customerAddressContextMenuItems}
              onContextMenu={(params) => setCellData(params.data)}
              className={`3xsm:!h-[62vh] md:!h-[72vh] ${selectedQuickFilterData.length > 0 ? 'lg:!h-[69vh]' : 'lg:!h-[74vh]'}`}
              gridName={GridNames.customerPortalAddressGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('addressPage.emptyState.title'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('addressPage.emptyState.description'),
                link:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('addressPage.emptyState.link'),
                onLinkAction: () => setIsModalOpen({ isOpen: true, isEdit: false }),
              }}
            />
          </div>
        </main>
      </div>
    </>
  );
};

export default AddressPage;
