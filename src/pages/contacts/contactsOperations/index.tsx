import { infoCircleOutlined } from '@/assets';
import { formErrorRegex } from '@/constant/Regex';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Divider, Form, Input, InputRef, Select, Space, Switch, Tooltip, Typography } from 'antd';
import MaskedInput from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { useCallback, useEffect, useState } from 'react';
import { useRef } from 'react';
import './ContactsOperations.css';
import { IAddContactForm, ValuesObject } from './contact.types';
import { useLanguage } from '@/hooks/useLanguage';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { ICustomerContact } from '@/api/contacts/contacts.types';

const ContactFormOperations: React.FC<IAddContactForm> = (props) => {
  const {
    form,
    onFinish,
    currentData,
    isAddContactModalOpen,
    setSelectedCategories,
    contactCategories,
  } = props;
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [formData, setFormData] = useState<ICustomerContact>();
  const inputPhoneRef = useRef<InputRef>(null);
  const { t } = useLanguage();
  const maskingInputPhone = useCallback((value: string) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    inputPhoneRef?.current?.focus();
  }, []);

  useEffect(() => {
    if (currentData) {
      setFormData(currentData);
    }
  }, [currentData]);

  useEffect(() => {
    if (!formData) return;
    form.setFieldsValue({
      ...formData,
      phoneCountryCode: formData.phoneCountryCode,
    });
    maskingInputPhone(formData?.phoneCountryCode);
  }, [form, formData, maskingInputPhone]);

  useEffect(() => {
    const data = formData?.phoneNumber;
    form.setFieldValue('phoneNumber', data);
  }, [form, formData?.phoneNumber, maskPhoneInput]);

  useEffect(() => {
    getCurrentCoordinates();
  }, []);

  function getCurrentCoordinates() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        getCountryFromCoordinates(latitude, longitude);
      });
    }
  }

  const getCountryFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
      );
      const data = await response.json();

      if (data && data.address) {
        const country = data.address.country;
        optionsForPrefix?.find((item) => {
          const countryMatches = item.geoCountryCode === country;
          if (countryMatches && !isAddContactModalOpen.isEdit) {
            form.setFieldValue('phoneCountryCode', item.label);
            maskingInputPhone(item.value);
          }
        });
      }
    } catch (error) {
      console.error('Error fetching country:', error);
    }
  };
  const handleCategoryOnChange = (value: (string | ValuesObject)[], values: ValuesObject[]) => {
    const formattedCategories = value.map((category) => {
      if (typeof category === 'string') {
        const existingCategory = values.find(
          (item) => item.value === category || item.label === category
        );
        if (existingCategory) {
          return { id: existingCategory.value, name: existingCategory.label };
        } else {
          return { name: category };
        }
      } else {
        return category;
      }
    });
    setSelectedCategories(formattedCategories as ValuesObject[]);
  };
  const phoneNumberPrefix = (
    <Form.Item className="contact-general-maskedInput" name={'phoneCountryCode'}>
      <Select
        placeholder="USA +1"
        options={optionsForPrefix}
        onChange={(value) => maskingInputPhone(value)}
      />
    </Form.Item>
  );
  return (
    <div className="text-2xl p-2 ">
      <Form
        name="add-contact-form"
        layout="vertical"
        form={form}
        onFinish={onFinish}
        preserve={false}
        className="contact-general-add-form"
      >
        <div className="flex flex-col">
          <div className="grid grid-cols-2 gap-x-5">
            <Form.Item
              validateFirst
              rules={[
                {
                  required: true,
                  message: `${t('dashboard.customer.columns.pleaseEnterYourName')}`,
                },
                {
                  whitespace: true,
                  message: `${t('dashboard.customer.columns.pleaseEnterYourName')}`,
                },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: `${t('common.errors.noMultipleWhiteSpace')}`,
                },
                {
                  pattern: formErrorRegex.NoSpecialCharacters,
                  message: `${t('common.errors.noSpacialCharacters')}`,
                },
              ]}
              labelAlign="left"
              className="contact-general-form-item"
              name={'name'}
              label={t('zonePage.colDefs.name')}
            >
              <Input
                min={3}
                maxLength={255}
                className="contact-general-input"
                placeholder={t('contacts.form.namePlaceholder')}
              />
            </Form.Item>
            <Space.Compact className="combined-masked-input customer-general-form-maskedItem !w-[100%]">
              <Form.Item
                validateFirst
                dependencies={['phoneCountryCode']}
                rules={[
                  {
                    validator: validateCountryAndValue(
                      form,
                      'phoneCountryCode',
                      'phone number',
                      true
                    ),
                  },
                  {
                    validator: (_, value) =>
                      validateMaskedInput(
                        value,
                        maskPhoneInput.length,
                        t('contacts.form.validPhoneNumberError')
                      ),
                  },
                ]}
                required
                className="contact-general-form-maskedItem !mb-[0px]"
                name="phoneNumber"
                label={t('addressPage.operationalForm.phoneNumber')}
              >
                <MaskedInput
                  ref={inputPhoneRef}
                  addonBefore={phoneNumberPrefix}
                  className="contact-general-maskedInput"
                  placeholder={t('contacts.form.phoneNumberPlaceholder')}
                  mask={maskPhoneInput.mask}
                />
              </Form.Item>
              <Form.Item name="phoneExtension" className="w-[30%] !mb-0">
                <Input
                  onKeyDown={(event) => numberFieldValidator(event, {})}
                  placeholder={t('contacts.form.phoneExtensionPlaceholder')}
                  maxLength={6}
                  className="customer-general-input mt-[30px] h-[40px]"
                />
              </Form.Item>
            </Space.Compact>

            <Form.Item
              rules={[
                {
                  required: true,
                  message: `${t('auth.emailRequired')}`,
                },
                {
                  pattern: formErrorRegex.ValidEmailOrNot,
                  message: `${t('auth.invalidEmail')}`,
                },
              ]}
              className="contact-general-form-item "
              name={'email'}
              label={t('contacts.form.email')}
            >
              <Input
                maxLength={255}
                className="contact-general-input"
                placeholder={t('contacts.form.emailPlaceholder')}
              />
            </Form.Item>
            <Form.Item
              className="contact-general-form-item"
              name={'categories'}
              rules={[
                { required: false },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
                {
                  validator: (_, value) => {
                    if (!value || value.length === 0) {
                      return Promise.resolve();
                    }
                    const invalidTags = value?.filter((tag: string) => tag.length > 255);
                    if (invalidTags.length > 0) {
                      return Promise.reject(
                        new Error(t('priceModifiers.maximumValueExceeded', { max: 255 }))
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              label={
                <span className="flex gap-1">
                  {t('contacts.form.department')}
                  <Tooltip title={t('contacts.form.departmentTooltip')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </Tooltip>
                </span>
              }
            >
              <Select
                maxTagTextLength={10}
                prefixCls="custom-select"
                mode="tags"
                placeholder={t('contacts.form.departmentPlaceholder')}
                style={{ width: '100%' }}
                tokenSeparators={[',']}
                options={contactCategories}
                onChange={(value, valueString) =>
                  handleCategoryOnChange(value, valueString as ValuesObject[])
                }
              />
            </Form.Item>
          </div>
          <Form.Item
            name="isActive"
            layout="horizontal"
            initialValue={true}
            className="contact-active-key-item"
            label={
              <span className="flex gap-2">
                {t('contacts.form.contactActive')}
                <Tooltip title={t('contacts.form.contactActiveTooltip')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch size="small" />
          </Form.Item>
        </div>
        <Divider orientation="left" orientationMargin={0} className="permission-divider">
          {t('contacts.form.permissionSetting')}
        </Divider>
        <Typography.Title className="contact-permission-typography">
          {t('contacts.form.permissions')}
        </Typography.Title>
        <Typography.Text className="!font-normal" type="secondary">
          {t('contacts.form.permissionSettingDescription')}
        </Typography.Text>
        <div className="flex w-full justify-between">
          <Form.Item
            name="address"
            layout="horizontal"
            className="contact-switch-key-item"
            label={
              <span className="flex gap-2 font-[600]">
                {t('contacts.form.addressPermission')}
                <Tooltip title={t('contacts.form.addressPermissionTooltip')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch size="small" />
          </Form.Item>

          <Form.Item
            name={'prices'}
            layout="horizontal"
            className="contact-switch-key-item"
            label={
              <span className="flex gap-2 font-[600]">
                {t('contacts.form.pricesPermission')}
                <Tooltip title={t('contacts.form.pricesPermissionTooltip')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch size="small" />
          </Form.Item>
          <Form.Item
            name={'invoice'}
            className="contact-switch-key-item"
            layout="horizontal"
            label={
              <span className="flex gap-2 font-[600]">
                {t('contacts.form.invoicePermission')}
                <Tooltip title={t('contacts.form.invoicePermissionTooltip')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch size="small" />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default ContactFormOperations;
