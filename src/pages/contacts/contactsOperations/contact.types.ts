import { ICustomerContact } from '@/api/contacts/contacts.types';
import { FormInstance } from 'antd';

export interface IIsOpenModal {
  isOpen: boolean;
  isEdit?: boolean;
}
export interface ValuesObject {
  value: string;
  label: string;
}
export interface IAddContactForm {
  form: FormInstance<ICustomerContact>;
  onFinish: (values: ICustomerContact) => void;
  currentData: ICustomerContact;
  isAddContactModalOpen: IIsOpenModal;
  setSelectedCategories: (selectedCategories: ValuesObject[]) => void;
  contactCategories: ValuesObject[];
}
