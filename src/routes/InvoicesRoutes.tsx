import { ROUTES } from '@/constant/RoutesConstant';
import { RouteObject } from 'react-router-dom';
import InvoicesPage from '@/pages/invoices';
import PermissionProtectedRoute from './PermissionProtectedRoute';
import ProtectedRoute from './ProtectedRoute';

export const InvoicesRoutes: RouteObject[] = [
  {
    path: ROUTES.INVOICES.LISTING,
    element: (
      <PermissionProtectedRoute
        element={<ProtectedRoute element={<InvoicesPage />} allowedRoles={['User', 'Tenant']} />}
        permissionFlag="invoices"
      />
    ),
  },
];
