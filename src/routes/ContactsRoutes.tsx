import { ROUTES } from '@/constant/RoutesConstant';
import { RouteObject } from 'react-router-dom';
import ContactPage from '@/pages/contacts';
import PermissionProtectedRoute from './PermissionProtectedRoute';
import ProtectedRoute from './ProtectedRoute';

export const ContactsRoutes: RouteObject[] = [
  {
    path: ROUTES.CONTACT.LISTING,
    element: (
      <PermissionProtectedRoute
        element={<ProtectedRoute element={<ContactPage />} allowedRoles={['User', 'Tenant']} />}
        permissionFlag="isPrimary"
      />
    ),
  },
];
