import { Navigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';

import { useMemo } from 'react';
import { useGetCurrentUser } from '@/api/auth/auth.service';
import Spinner from '@/components/common/spinner/Spinner';

interface PermissionProtectedRouteProps {
  element: React.ReactElement;
  permissionFlag?: 'address' | 'invoices' | 'prices' | 'isPrimary';
}

/**
 * A route component that checks both role-based access and specific permission flags
 * before allowing access to the protected route.
 *
 * @param element The component to render if access is granted
 * @param permissionFlag Optional permission flag to check (address, invoices, prices, or isPrimary)
 */

const PermissionProtectedRoute: React.FC<PermissionProtectedRouteProps> = ({
  element,
  permissionFlag,
}) => {
  const { data: userInfo, isPending } = useGetCurrentUser();

  const hasAccess = useMemo(() => {
    // First check if user is authenticated and has the required role
    if (!userInfo) {
      return false;
    }

    // If no permission flag is specified, just check the role
    if (!permissionFlag) {
      return true;
    }

    // Check the specific permission flag
    if (permissionFlag === 'isPrimary') {
      return !!userInfo?.isPrimary;
    } else {
      return !!userInfo?.permissions?.[permissionFlag];
    }
  }, [userInfo, permissionFlag]);

  if (isPending) {
    return <Spinner />;
  }

  if (!userInfo && !isPending) {
    return <Navigate to={ROUTES.COMMON.LOGIN} replace />;
  }

  if (!hasAccess) {
    return <Navigate to={ROUTES.ORDER.ORDER_ENTRY} replace />;
  }

  return element;
};

export default PermissionProtectedRoute;
