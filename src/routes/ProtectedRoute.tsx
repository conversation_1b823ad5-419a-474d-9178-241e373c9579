import { Navigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { URoles, Roles } from '@/types/enums/Roles';
import { useMemo } from 'react';
import { getUserInfoFromStorage } from '@/lib/helper/userHelper';
import { getStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';

interface ProtectedRouteProps {
  element: React.ReactElement;
  allowedRoles: URoles[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ element, allowedRoles }) => {
  const isUserAuthenticated = getStorageItem(StorageKeys.IS_AUTHENTICATED);

  const currentUser = getUserInfoFromStorage();
  const hasRole = useMemo(
    () => allowedRoles.includes(currentUser?.role as Roles),
    [currentUser, allowedRoles]
  );

  if (!isUserAuthenticated) {
    return <Navigate to={ROUTES.COMMON.LOGIN} replace />;
  }

  if (!hasRole) {
    return <Navigate to={ROUTES.COMMON.UN_AUTHORIZED} replace />;
  }

  return element;
};

export default ProtectedRoute;
