import React, { Suspense } from 'react';
import { RouteObject, Navigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import StatusFallbackPage from '@/components/common/statusFallbackPage/StatusFallbackPage';
import { PublicRoute } from './PublicRoutes';
import Spinner from '@/components/common/spinner/Spinner';
import ForgetPasswordPage from '@/pages/login/forgotPassword';
import ResetpasswordPage from '@/pages/login/resetPassword';
import OTPVerificationPage from '@/pages/login/otpVerification';
import { OrderForm } from '@/pages/orderEntry/orderEntryForms';
import BillOfLadingPDFView from '@/pages/orders/billOfLading';
import ShippingLabelPDFView from '@/pages/orders/shipingLabePdfView';
import WayBillPDFView from '@/pages/orders/wayBill';
import OrderTrackingEmail from '@/pages/orders/orderTrackingMailTemplate';
import InvoiceTemplateView from '@/pages/invoices/invoiceTemplate';

const LoginPage = React.lazy(() => import('../pages/login/LoginPage'));

export const CommonRoutes: RouteObject[] = [
  {
    path: ROUTES.COMMON.LOGIN,
    element: (
      <PublicRoute>
        <Suspense
          fallback={
            <div className="h-dvh">
              <Spinner />
            </div>
          }
        >
          <LoginPage />
        </Suspense>
      </PublicRoute>
    ),
  },
  {
    path: ROUTES.COMMON.DEFAULT,
    element: <Navigate to={ROUTES.ORDER.ORDER_ENTRY} replace />,
  },
  {
    path: ROUTES.COMMON.DASHBOARD,
    element: <OrderForm />,
  },
  {
    path: ROUTES.COMMON.UN_AUTHORIZED,
    element: <StatusFallbackPage status={403} />,
  },
  {
    path: ROUTES.COMMON.NOT_EXIST,
    element: <Navigate to={ROUTES.ORDER.ORDER_ENTRY} replace />,
  },
  {
    path: ROUTES.COMMON.INTERNAL_SERVER_ERROR,
    element: <StatusFallbackPage status={500} />,
  },
  {
    path: ROUTES.COMMON.FORGET_PASSWORD,
    element: <ForgetPasswordPage />,
  },
  {
    path: ROUTES.COMMON.OTP_VERIFICATION,
    element: <OTPVerificationPage />,
  },
  {
    path: ROUTES.COMMON.RESET_PASSWORD,
    element: <ResetpasswordPage />,
  },
  {
    path: '/bill',
    element: <BillOfLadingPDFView />,
  },
  {
    path: '/shipping',
    element: <ShippingLabelPDFView />,
  },
  {
    path: '/waybill',
    element: <WayBillPDFView />,
  },
  {
    path: '/mail',
    element: <OrderTrackingEmail />,
  },
  {
    path: '/invoice',
    element: <InvoiceTemplateView />,
  },

  {
    path: '*',
    element: <Navigate to={ROUTES.ORDER.ORDER_ENTRY} replace />,
  },
];
