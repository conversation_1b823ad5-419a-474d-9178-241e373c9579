import { ROUTES } from '@/constant/RoutesConstant';
import ProtectedRoute from './ProtectedRoute';
import { RouteObject } from 'react-router-dom';
import OrderListPage from '@/pages/orders';
import OrderDetailsPage from '@/pages/orders/orderDetails';
import { OrderForm } from '@/pages/orderEntry/orderEntryForms';

export const OrderRoutes: RouteObject[] = [
  {
    path: ROUTES.ORDER.ORDER_ENTRY,
    element: <ProtectedRoute element={<OrderForm />} allowedRoles={['User', 'Tenant']} />,
  },
  {
    path: ROUTES.ORDER.ORDER_EDIT,
    element: <ProtectedRoute element={<OrderForm />} allowedRoles={['User', 'Tenant']} />,
  },
  {
    path: ROUTES.ORDER.LISTING,
    element: <ProtectedRoute element={<OrderListPage />} allowedRoles={['User', 'Tenant']} />,
  },
  {
    path: ROUTES.ORDER.ORDER_DETAILS,
    element: <ProtectedRoute element={<OrderDetailsPage />} allowedRoles={['User', 'Tenant']} />,
  },
];
