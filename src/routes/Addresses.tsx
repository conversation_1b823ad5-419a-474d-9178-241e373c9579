import { ROUTES } from '@/constant/RoutesConstant';
import { RouteObject } from 'react-router-dom';
import AddressesPage from '@/pages/addresses';
import PermissionProtectedRoute from './PermissionProtectedRoute';
import ProtectedRoute from './ProtectedRoute';

export const AddressesRoutes: RouteObject[] = [
  {
    path: ROUTES.ADDRESSES.LISTING,
    element: (
      <PermissionProtectedRoute
        element={<ProtectedRoute element={<AddressesPage />} allowedRoles={['User', 'Tenant']} />}
        permissionFlag="address"
      />
    ),
  },
];
