/** @type {import('tailwindcss').Config} */
import { screens as _screens } from 'tailwindcss/defaultTheme';
// import './src/styles/theme.css';

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    fontFamily: {
      Inter: 'Inter',
      //TODO: LOAD FONT WITH TAILWIND
    },
    screens: {
      '3xsm': '320px',
      '2xsm': '375px',
      sm: '425px',
      md: '950px',
      lg: '1200px',
      '3xl': '1920px',
      '4xl': '2350px',
      ..._screens,
    },
    extend: {
      dropShadow: {
        collapseButton: '7px 0px 5px rgb(0 0 0 / 6%)',
      },
      boxShadow: {
        tooltip: '0px 2px 8px 0px rgba(0, 0, 0, 0.15)',
        contextMenu:
          ' rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;',
      },
      colors: {
        white: 'var(--white)',
        grey: {
          25: 'var(--grey-25)',
          50: 'var(--grey-50)',
          100: 'var(--grey-100)',
          200: 'var(--grey-200)',
          300: 'var(--grey-300)',
          400: 'var(--grey-400)',
          500: 'var(--grey-500)',
          600: 'var(--grey-600)',
          800: 'var(--grey-800)',
        },
        primary: {
          25: 'var(--primary-25)',
          50: 'var(--primary-50)',
          100: 'var(--primary-100)',
          200: 'var(--primary-200)',
          300: 'var(--primary-300)',
          400: 'var(--primary-400)',
          500: 'var(--primary-500)',
          600: 'var(--primary-600)',
          800: 'var(--primary-800)',
          900: 'var(--primary-900)',
        },
        error: {
          25: 'var(--error-25)',
          50: 'var(--error-50)',
          100: 'var(--error-100)',
          200: 'var(--error-200)',
          300: 'var(--error-300)',
          400: 'var(--error-400)',
          500: 'var(--error-500)',
          600: 'var(--error-600)',
          800: 'var(--error-800)',
        },
        warning: {
          25: 'var(--warning-25)',
          50: 'var(--warning-50)',
          100: 'var(--warning-100)',
          200: 'var(--warning-200)',
          300: 'var(--warning-300)',
          400: 'var(--warning-400)',
          500: 'var(--warning-500)',
          600: 'var(--warning-600)',
          800: 'var(--warning-800)',
        },
        success: {
          25: 'var(--success-25)',
          50: 'var(--success-50)',
          100: 'var(--success-100)',
          200: 'var(--success-200)',
          300: 'var(--success-300)',
          400: 'var(--success-400)',
          500: 'var(--success-500)',
          600: 'var(--success-600)',
          800: 'var(--success-800)',
        },
      },
      fontSize: {
        'display-md': ['36px', '44px'] /* [Font size, Line height] */,
        'display-sm': ['30px', '38px'],
        'display-xs': ['24px', '32px'],
        'text-xl': ['20px', '30px'],
        'text-lg': ['18px', '28px'],
        'text-md': ['16px', '24px'],
        'text-sm': ['14px', '20px'],
        'text-xs': ['12px', '18px'],
      },
      fontWeight: {
        regular: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      height: {
        'screen-80': '80vh',
        'screen-85': '85vh',
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities({
        '.clip-path-collapse-rectangle': {
          clipPath: 'polygon(-14px 0px, 90% 20px, 90% 50%, 90% 75%, -14px 100%, 0% 92%)',
        },
      });
    },
  ],
};
